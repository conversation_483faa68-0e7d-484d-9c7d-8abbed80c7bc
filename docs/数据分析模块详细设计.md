# 数据分析模块详细设计文档

## 1. 模块概述

数据分析模块负责对采集到的用户行为数据进行深度分析，提供用户行为分析、商品分析、流量分析等功能，为电商运营决策提供数据支持。

## 2. 数据库设计

### 2.1 分析结果表（analysis_results）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BIGINT | - | 是 | AUTO_INCREMENT | 结果ID，主键 |
| analysis_type | VARCHAR | 50 | 是 | - | 分析类型 |
| analysis_name | VARCHAR | 100 | 是 | - | 分析名称 |
| date_range_start | DATE | - | 是 | - | 分析开始日期 |
| date_range_end | DATE | - | 是 | - | 分析结束日期 |
| result_data | JSON | - | 是 | - | 分析结果数据 |
| status | ENUM | - | 是 | 'pending' | 状态：pending/completed/failed |
| created_by | INT | - | 是 | - | 创建人ID |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 更新时间 |

### 2.2 用户标签表（user_tags）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | 是 | AUTO_INCREMENT | 标签ID，主键 |
| user_id | INT | - | 是 | - | 用户ID，外键 |
| tag_type | VARCHAR | 50 | 是 | - | 标签类型 |
| tag_value | VARCHAR | 100 | 是 | - | 标签值 |
| score | DECIMAL | 5,2 | 否 | - | 标签分数 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 更新时间 |

### 2.3 商品热度表（product_popularity）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | 是 | AUTO_INCREMENT | 记录ID，主键 |
| product_id | INT | - | 是 | - | 商品ID，外键 |
| date | DATE | - | 是 | - | 统计日期 |
| view_count | INT | - | 是 | 0 | 浏览次数 |
| click_count | INT | - | 是 | 0 | 点击次数 |
| cart_count | INT | - | 是 | 0 | 加购次数 |
| order_count | INT | - | 是 | 0 | 下单次数 |
| conversion_rate | DECIMAL | 5,4 | 是 | 0 | 转化率 |
| popularity_score | DECIMAL | 8,2 | 是 | 0 | 热度分数 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

### 2.4 漏斗分析表（funnel_analysis）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | 是 | AUTO_INCREMENT | 分析ID，主键 |
| funnel_name | VARCHAR | 100 | 是 | - | 漏斗名称 |
| step_name | VARCHAR | 100 | 是 | - | 步骤名称 |
| step_order | INT | - | 是 | - | 步骤顺序 |
| date | DATE | - | 是 | - | 统计日期 |
| user_count | INT | - | 是 | 0 | 用户数量 |
| conversion_rate | DECIMAL | 5,4 | 是 | 0 | 转化率 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

## 3. API接口设计

### 3.1 用户行为分析

#### 3.1.1 用户活跃度分析

**接口地址**: `GET /api/analysis/user-activity`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)
- `granularity`: 粒度 (daily/weekly/monthly)

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "total_users": 10000,
        "active_users": 6500,
        "activity_rate": 0.65,
        "daily_data": [
            {
                "date": "2024-01-15",
                "total_users": 1000,
                "active_users": 650,
                "new_users": 50,
                "returning_users": 600
            }
        ]
    }
}
```

#### 3.1.2 用户留存分析

**接口地址**: `GET /api/analysis/user-retention`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期
- `retention_type`: 留存类型 (1day/7day/30day)

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "cohort_analysis": [
            {
                "cohort_date": "2024-01-01",
                "new_users": 100,
                "day_1": 0.85,
                "day_7": 0.45,
                "day_30": 0.25
            }
        ],
        "average_retention": {
            "day_1": 0.78,
            "day_7": 0.42,
            "day_30": 0.28
        }
    }
}
```

#### 3.1.3 用户路径分析

**接口地址**: `GET /api/analysis/user-path`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期
- `start_page`: 起始页面（可选）
- `max_depth`: 最大深度（默认5）

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "path_flows": [
            {
                "path": ["/home", "/category", "/product/100", "/cart"],
                "user_count": 1234,
                "conversion_rate": 0.15
            }
        ],
        "page_transitions": [
            {
                "from_page": "/home",
                "to_page": "/category",
                "transition_count": 5678,
                "bounce_rate": 0.35
            }
        ]
    }
}
```

#### 3.1.4 RFM用户价值分析

**接口地址**: `GET /api/analysis/rfm-analysis`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "rfm_segments": [
            {
                "segment": "Champions",
                "user_count": 500,
                "percentage": 5.0,
                "avg_recency": 10,
                "avg_frequency": 15,
                "avg_monetary": 5000
            },
            {
                "segment": "Loyal Customers",
                "user_count": 1200,
                "percentage": 12.0,
                "avg_recency": 25,
                "avg_frequency": 8,
                "avg_monetary": 2000
            }
        ],
        "user_distribution": {
            "high_value": 1700,
            "medium_value": 3500,
            "low_value": 4800
        }
    }
}
```

### 3.2 商品分析

#### 3.2.1 商品热度排行

**接口地址**: `GET /api/analysis/product-popularity`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期
- `category_id`: 分类ID（可选）
- `sort_by`: 排序字段 (view_count/click_count/conversion_rate)
- `limit`: 返回数量（默认20）

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "products": [
            {
                "product_id": 100,
                "product_name": "iPhone 15",
                "category": "手机数码",
                "view_count": 15678,
                "click_count": 3456,
                "cart_count": 789,
                "order_count": 234,
                "conversion_rate": 0.0676,
                "popularity_score": 8.5
            }
        ],
        "summary": {
            "total_products": 1000,
            "avg_conversion_rate": 0.045
        }
    }
}
```

#### 3.2.2 商品转化率分析

**接口地址**: `GET /api/analysis/product-conversion`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `product_id`: 商品ID（可选）
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "funnel_data": {
            "product_view": 10000,
            "product_click": 3000,
            "add_to_cart": 800,
            "checkout": 300,
            "order_complete": 250
        },
        "conversion_rates": {
            "view_to_click": 0.30,
            "click_to_cart": 0.267,
            "cart_to_checkout": 0.375,
            "checkout_to_order": 0.833
        },
        "trend_data": [
            {
                "date": "2024-01-15",
                "conversion_rate": 0.025
            }
        ]
    }
}
```

#### 3.2.3 商品关联分析

**接口地址**: `GET /api/analysis/product-association`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `product_id`: 商品ID（可选）
- `start_date`: 开始日期
- `end_date`: 结束日期
- `min_support`: 最小支持度（默认0.01）

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "associations": [
            {
                "product_a": {
                    "id": 100,
                    "name": "iPhone 15"
                },
                "product_b": {
                    "id": 101,
                    "name": "iPhone 15 保护壳"
                },
                "support": 0.15,
                "confidence": 0.75,
                "lift": 2.3
            }
        ],
        "frequently_bought_together": [
            {
                "products": [100, 101, 102],
                "support": 0.08,
                "purchase_count": 156
            }
        ]
    }
}
```

### 3.3 流量分析

#### 3.3.1 网站流量趋势

**接口地址**: `GET /api/analysis/traffic-trend`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期
- `granularity`: 粒度 (hourly/daily/weekly)

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "summary": {
            "total_pv": 1000000,
            "total_uv": 150000,
            "avg_session_duration": 480,
            "bounce_rate": 0.35
        },
        "trend_data": [
            {
                "time": "2024-01-15 10:00:00",
                "pv": 5678,
                "uv": 1234,
                "session_count": 890,
                "bounce_rate": 0.32
            }
        ],
        "peak_hours": [
            {"hour": 10, "pv": 45678},
            {"hour": 14, "pv": 43210},
            {"hour": 20, "pv": 41234}
        ]
    }
}
```

#### 3.3.2 页面访问热力图

**接口地址**: `GET /api/analysis/page-heatmap`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期
- `page_url`: 页面URL（可选）

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "pages": [
            {
                "page_url": "/home",
                "page_title": "首页",
                "total_views": 50000,
                "unique_views": 15000,
                "avg_stay_time": 120,
                "bounce_rate": 0.25
            }
        ],
        "entry_pages": [
            {
                "page_url": "/home",
                "entry_count": 25000,
                "percentage": 50.0
            }
        ],
        "exit_pages": [
            {
                "page_url": "/cart",
                "exit_count": 8000,
                "exit_rate": 0.45
            }
        ]
    }
}
```

#### 3.3.3 来源渠道分析

**接口地址**: `GET /api/analysis/traffic-source`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "source_summary": [
            {
                "source_type": "direct",
                "user_count": 50000,
                "session_count": 75000,
                "percentage": 33.3,
                "conversion_rate": 0.08
            },
            {
                "source_type": "search",
                "user_count": 40000,
                "session_count": 55000,
                "percentage": 26.7,
                "conversion_rate": 0.12
            },
            {
                "source_type": "social",
                "user_count": 30000,
                "session_count": 35000,
                "percentage": 20.0,
                "conversion_rate": 0.05
            }
        ],
        "referrer_details": [
            {
                "referrer": "www.baidu.com",
                "user_count": 25000,
                "conversion_rate": 0.15
            }
        ]
    }
}
```

### 3.4 高级分析功能

#### 3.4.1 漏斗分析

**接口地址**: `POST /api/analysis/funnel`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "funnel_name": "string (必填, 漏斗名称)",
    "start_date": "string (必填, 开始日期)",
    "end_date": "string (必填, 结束日期)",
    "steps": [
        {
            "name": "页面访问",
            "event_type": "pageview",
            "conditions": {
                "page_url": "/product/*"
            }
        },
        {
            "name": "加入购物车",
            "event_type": "behavior",
            "conditions": {
                "behavior_type": "add_cart"
            }
        },
        {
            "name": "下单购买",
            "event_type": "behavior",
            "conditions": {
                "behavior_type": "purchase"
            }
        }
    ]
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "分析完成",
    "data": {
        "funnel_id": 123,
        "steps": [
            {
                "step_name": "页面访问",
                "user_count": 10000,
                "conversion_rate": 1.0
            },
            {
                "step_name": "加入购物车",
                "user_count": 2000,
                "conversion_rate": 0.2
            },
            {
                "step_name": "下单购买",
                "user_count": 300,
                "conversion_rate": 0.15
            }
        ],
        "overall_conversion": 0.03
    }
}
```

#### 3.4.2 用户细分分析

**接口地址**: `POST /api/analysis/user-segmentation`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "segment_name": "string (必填, 细分名称)",
    "start_date": "string (必填, 开始日期)",
    "end_date": "string (必填, 结束日期)",
    "criteria": [
        {
            "field": "total_orders",
            "operator": ">=",
            "value": 5
        },
        {
            "field": "total_amount",
            "operator": ">=",
            "value": 1000
        }
    ]
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "分析完成",
    "data": {
        "segment_id": 456,
        "user_count": 1500,
        "percentage": 15.0,
        "characteristics": {
            "avg_order_count": 8.5,
            "avg_order_amount": 2500.0,
            "avg_session_duration": 600,
            "top_categories": ["电子产品", "服装", "家居"]
        },
        "behavior_patterns": {
            "preferred_time": "晚上8-10点",
            "preferred_device": "移动端",
            "purchase_frequency": "每月2-3次"
        }
    }
}
```

## 4. 数据处理算法

### 4.1 用户活跃度计算

```python
def calculate_user_activity(start_date, end_date):
    """
    计算用户活跃度
    """
    # 获取时间段内的所有用户行为数据
    behaviors = UserBehavior.objects.filter(
        created_at__range=[start_date, end_date]
    )
    
    # 按用户分组计算活跃度
    user_activity = {}
    for behavior in behaviors:
        user_id = behavior.user_id
        if user_id not in user_activity:
            user_activity[user_id] = {
                'page_views': 0,
                'clicks': 0,
                'orders': 0,
                'sessions': set()
            }
        
        user_activity[user_id]['sessions'].add(behavior.session_id)
        if behavior.behavior_type == 'view':
            user_activity[user_id]['page_views'] += 1
        elif behavior.behavior_type == 'click':
            user_activity[user_id]['clicks'] += 1
        elif behavior.behavior_type == 'purchase':
            user_activity[user_id]['orders'] += 1
    
    return user_activity
```

### 4.2 RFM分析算法

```python
def calculate_rfm_scores(users_data):
    """
    计算RFM分数
    R: Recency (最近购买时间)
    F: Frequency (购买频率) 
    M: Monetary (购买金额)
    """
    import pandas as pd
    from datetime import datetime
    
    df = pd.DataFrame(users_data)
    now = datetime.now()
    
    # 计算R分数 (天数越少分数越高)
    df['recency_days'] = (now - df['last_order_date']).dt.days
    df['R'] = pd.qcut(df['recency_days'], 5, labels=[5,4,3,2,1])
    
    # 计算F分数 (频率越高分数越高)
    df['F'] = pd.qcut(df['order_frequency'].rank(method='first'), 5, labels=[1,2,3,4,5])
    
    # 计算M分数 (金额越高分数越高)
    df['M'] = pd.qcut(df['total_amount'], 5, labels=[1,2,3,4,5])
    
    # 组合RFM分数
    df['RFM_Score'] = df['R'].astype(str) + df['F'].astype(str) + df['M'].astype(str)
    
    return df
```

### 4.3 商品关联规则挖掘

```python
def find_association_rules(transactions, min_support=0.01, min_confidence=0.1):
    """
    使用Apriori算法挖掘关联规则
    """
    from mlxtend.frequent_patterns import apriori, association_rules
    import pandas as pd
    
    # 转换为一热编码格式
    basket = transactions.groupby(['order_id', 'product_id'])['quantity'].sum().unstack().fillna(0)
    basket_sets = basket.applymap(lambda x: 1 if x > 0 else 0)
    
    # 挖掘频繁项集
    frequent_itemsets = apriori(basket_sets, min_support=min_support, use_colnames=True)
    
    # 生成关联规则
    rules = association_rules(frequent_itemsets, metric="confidence", min_threshold=min_confidence)
    
    return rules
```

## 5. 分析任务调度

### 5.1 定时任务配置

```python
# Celery定时任务
from celery import Celery
from celery.schedules import crontab

app = Celery('analytics')

@app.task
def daily_user_analysis():
    """每日用户行为分析"""
    from datetime import date, timedelta
    
    yesterday = date.today() - timedelta(days=1)
    
    # 执行用户活跃度分析
    activity_data = calculate_user_activity(yesterday, yesterday)
    
    # 保存分析结果
    save_analysis_result('user_activity', yesterday, activity_data)

@app.task  
def weekly_product_analysis():
    """每周商品分析"""
    # 执行商品热度分析
    # 执行商品关联分析
    pass

# 定时任务调度
app.conf.beat_schedule = {
    'daily-user-analysis': {
        'task': 'daily_user_analysis',
        'schedule': crontab(hour=1, minute=0),  # 每天凌晨1点执行
    },
    'weekly-product-analysis': {
        'task': 'weekly_product_analysis', 
        'schedule': crontab(hour=2, minute=0, day_of_week=1),  # 每周一凌晨2点执行
    },
}
```

## 6. 缓存策略

### 6.1 Redis缓存配置

```python
import redis
import json
from datetime import timedelta

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_analysis_result(cache_key, data, expire_hours=24):
    """缓存分析结果"""
    redis_client.setex(
        cache_key, 
        timedelta(hours=expire_hours), 
        json.dumps(data, ensure_ascii=False)
    )

def get_cached_result(cache_key):
    """获取缓存的分析结果"""
    cached_data = redis_client.get(cache_key)
    if cached_data:
        return json.loads(cached_data)
    return None
```

## 7. 性能优化

### 7.1 查询优化
- 合理使用数据库索引
- 分页查询大数据集
- 使用聚合查询减少数据传输

### 7.2 计算优化
- 异步处理复杂分析任务
- 预计算常用分析结果
- 使用内存数据库加速计算

### 7.3 存储优化
- 历史数据分表存储
- 冷热数据分离
- 定期数据归档
