# 用户管理模块详细设计文档

## 1. 模块概述

用户管理模块负责系统的用户注册、登录、权限控制以及用户信息管理功能。包括管理员用户和普通用户两种角色。

## 2. 数据库设计

### 2.1 用户表（users）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | 是 | AUTO_INCREMENT | 用户ID，主键 |
| username | VARCHAR | 50 | 是 | - | 用户名，唯一 |
| email | VARCHAR | 100 | 是 | - | 邮箱，唯一 |
| password | VARCHAR | 128 | 是 | - | 密码（加密存储） |
| real_name | VARCHAR | 50 | 否 | - | 真实姓名 |
| phone | VARCHAR | 20 | 否 | - | 手机号 |
| avatar | VARCHAR | 255 | 否 | - | 头像URL |
| role | ENUM | - | 是 | 'user' | 用户角色：admin/user |
| status | ENUM | - | 是 | 'active' | 用户状态：active/inactive/banned |
| last_login | DATETIME | - | 否 | - | 最后登录时间 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 更新时间 |

### 2.2 用户会话表（user_sessions）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | 是 | AUTO_INCREMENT | 会话ID，主键 |
| user_id | INT | - | 是 | - | 用户ID，外键 |
| session_key | VARCHAR | 255 | 是 | - | 会话密钥 |
| ip_address | VARCHAR | 45 | 否 | - | IP地址 |
| user_agent | TEXT | - | 否 | - | 用户代理 |
| expires_at | DATETIME | - | 是 | - | 过期时间 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

## 3. API接口设计

### 3.1 用户注册

**接口地址**: `POST /api/auth/register`

**请求参数**:
```json
{
    "username": "string (必填, 3-50字符)",
    "email": "string (必填, 邮箱格式)",
    "password": "string (必填, 6-20字符)",
    "confirm_password": "string (必填, 确认密码)",
    "real_name": "string (可选, 最大50字符)",
    "phone": "string (可选, 手机号格式)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "注册成功",
    "data": {
        "user_id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    }
}
```

**错误响应**:
```json
{
    "code": 400,
    "message": "用户名已存在",
    "errors": {
        "username": ["该用户名已被注册"]
    }
}
```

### 3.2 用户登录

**接口地址**: `POST /api/auth/login`

**请求参数**:
```json
{
    "username": "string (必填, 用户名或邮箱)",
    "password": "string (必填, 密码)",
    "remember_me": "boolean (可选, 是否记住登录)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "user": {
            "id": 1,
            "username": "testuser",
            "email": "<EMAIL>",
            "real_name": "测试用户",
            "role": "user",
            "avatar": "http://example.com/avatar.jpg"
        },
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expires_in": 3600
    }
}
```

### 3.3 用户登出

**接口地址**: `POST /api/auth/logout`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "登出成功"
}
```

### 3.4 获取当前用户信息

**接口地址**: `GET /api/auth/me`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "real_name": "测试用户",
        "phone": "13800138000",
        "avatar": "http://example.com/avatar.jpg",
        "role": "user",
        "status": "active",
        "last_login": "2024-01-15 10:30:00",
        "created_at": "2024-01-01 09:00:00"
    }
}
```

### 3.5 更新用户信息

**接口地址**: `PUT /api/auth/profile`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "real_name": "string (可选, 最大50字符)",
    "phone": "string (可选, 手机号格式)",
    "avatar": "string (可选, 头像URL)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "更新成功",
    "data": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "real_name": "新的真实姓名",
        "phone": "13800138001",
        "avatar": "http://example.com/new_avatar.jpg"
    }
}
```

### 3.6 修改密码

**接口地址**: `PUT /api/auth/change-password`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "old_password": "string (必填, 原密码)",
    "new_password": "string (必填, 新密码, 6-20字符)",
    "confirm_password": "string (必填, 确认新密码)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "密码修改成功"
}
```

### 3.7 管理员功能 - 用户列表

**接口地址**: `GET /api/admin/users`

**请求头**: `Authorization: Bearer {admin_token}`

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `search`: 搜索关键词（用户名或邮箱）
- `role`: 角色筛选（admin/user）
- `status`: 状态筛选（active/inactive/banned）

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "users": [
            {
                "id": 1,
                "username": "testuser",
                "email": "<EMAIL>",
                "real_name": "测试用户",
                "role": "user",
                "status": "active",
                "last_login": "2024-01-15 10:30:00",
                "created_at": "2024-01-01 09:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_count": 100,
            "per_page": 20
        }
    }
}
```

### 3.8 管理员功能 - 更新用户状态

**接口地址**: `PUT /api/admin/users/{user_id}/status`

**请求头**: `Authorization: Bearer {admin_token}`

**请求参数**:
```json
{
    "status": "string (必填, active/inactive/banned)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "用户状态更新成功"
}
```

## 4. 前端页面设计

### 4.1 登录页面 (`/login`)
- 用户名/邮箱输入框
- 密码输入框
- 记住登录状态复选框
- 登录按钮
- 注册链接

### 4.2 注册页面 (`/register`)
- 用户名输入框
- 邮箱输入框
- 密码输入框
- 确认密码输入框
- 真实姓名输入框（可选）
- 手机号输入框（可选）
- 注册按钮
- 登录链接

### 4.3 个人中心页面 (`/profile`)
- 用户信息展示
- 头像上传组件
- 信息编辑表单
- 密码修改表单

### 4.4 管理员用户管理页面 (`/admin/users`)
- 用户列表表格
- 搜索和筛选功能
- 用户状态管理
- 分页组件

## 5. 安全考虑

### 5.1 密码安全
- 使用bcrypt进行密码加密
- 密码强度验证
- 防止暴力破解（登录次数限制）

### 5.2 会话管理
- JWT token认证
- Token过期时间控制
- 安全的会话存储

### 5.3 权限控制
- 角色基础访问控制(RBAC)
- 接口权限验证
- 前端路由守卫

## 6. 验证规则

### 6.1 用户名验证
- 长度：3-50字符
- 格式：字母、数字、下划线
- 唯一性检查

### 6.2 邮箱验证
- 标准邮箱格式
- 唯一性检查

### 6.3 密码验证
- 长度：6-20字符
- 包含字母和数字
- 特殊字符可选

### 6.4 手机号验证
- 中国大陆手机号格式
- 11位数字
