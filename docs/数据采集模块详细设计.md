# 数据采集模块详细设计文档

## 1. 模块概述

数据采集模块负责收集电商平台的用户行为数据，包括页面浏览、商品点击、搜索行为、购物车操作、订单数据等。支持实时数据采集和批量数据导入功能。

## 2. 数据库设计

### 2.1 用户行为表（user_behaviors）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BIGINT | - | 是 | AUTO_INCREMENT | 行为ID，主键 |
| user_id | INT | - | 否 | - | 用户ID，外键 |
| session_id | VARCHAR | 100 | 是 | - | 会话ID |
| behavior_type | ENUM | - | 是 | - | 行为类型：view/click/search/add_cart/remove_cart/purchase |
| target_type | ENUM | - | 否 | - | 目标类型：product/category/page |
| target_id | INT | - | 否 | - | 目标ID |
| page_url | VARCHAR | 500 | 否 | - | 页面URL |
| referrer_url | VARCHAR | 500 | 否 | - | 来源URL |
| ip_address | VARCHAR | 45 | 否 | - | IP地址 |
| user_agent | TEXT | - | 否 | - | 用户代理 |
| device_type | ENUM | - | 否 | - | 设备类型：desktop/mobile/tablet |
| browser | VARCHAR | 50 | 否 | - | 浏览器 |
| os | VARCHAR | 50 | 否 | - | 操作系统 |
| location | VARCHAR | 100 | 否 | - | 地理位置 |
| duration | INT | - | 否 | - | 停留时长（秒） |
| extra_data | JSON | - | 否 | - | 额外数据 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

### 2.2 页面访问表（page_views）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BIGINT | - | 是 | AUTO_INCREMENT | 访问ID，主键 |
| user_id | INT | - | 否 | - | 用户ID，外键 |
| session_id | VARCHAR | 100 | 是 | - | 会话ID |
| page_url | VARCHAR | 500 | 是 | - | 页面URL |
| page_title | VARCHAR | 200 | 否 | - | 页面标题 |
| referrer_url | VARCHAR | 500 | 否 | - | 来源URL |
| ip_address | VARCHAR | 45 | 否 | - | IP地址 |
| user_agent | TEXT | - | 否 | - | 用户代理 |
| load_time | INT | - | 否 | - | 页面加载时间（毫秒） |
| stay_time | INT | - | 否 | - | 页面停留时间（秒） |
| scroll_depth | DECIMAL | 5,2 | 否 | - | 滚动深度百分比 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

### 2.3 搜索记录表（search_logs）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BIGINT | - | 是 | AUTO_INCREMENT | 搜索ID，主键 |
| user_id | INT | - | 否 | - | 用户ID，外键 |
| session_id | VARCHAR | 100 | 是 | - | 会话ID |
| search_keyword | VARCHAR | 200 | 是 | - | 搜索关键词 |
| search_type | ENUM | - | 是 | 'product' | 搜索类型：product/category/article |
| result_count | INT | - | 否 | - | 结果数量 |
| click_position | INT | - | 否 | - | 点击位置 |
| click_product_id | INT | - | 否 | - | 点击商品ID |
| ip_address | VARCHAR | 45 | 否 | - | IP地址 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

### 2.4 商品表（products）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | 是 | AUTO_INCREMENT | 商品ID，主键 |
| name | VARCHAR | 200 | 是 | - | 商品名称 |
| category_id | INT | - | 是 | - | 分类ID |
| brand | VARCHAR | 100 | 否 | - | 品牌 |
| price | DECIMAL | 10,2 | 是 | - | 价格 |
| original_price | DECIMAL | 10,2 | 否 | - | 原价 |
| description | TEXT | - | 否 | - | 商品描述 |
| image_url | VARCHAR | 500 | 否 | - | 商品图片 |
| stock | INT | - | 是 | 0 | 库存 |
| status | ENUM | - | 是 | 'active' | 状态：active/inactive/deleted |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 更新时间 |

### 2.5 订单表（orders）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BIGINT | - | 是 | AUTO_INCREMENT | 订单ID，主键 |
| order_no | VARCHAR | 50 | 是 | - | 订单号，唯一 |
| user_id | INT | - | 是 | - | 用户ID，外键 |
| total_amount | DECIMAL | 10,2 | 是 | - | 订单总额 |
| discount_amount | DECIMAL | 10,2 | 否 | 0 | 优惠金额 |
| shipping_fee | DECIMAL | 10,2 | 否 | 0 | 运费 |
| payment_method | ENUM | - | 否 | - | 支付方式：alipay/wechat/credit_card |
| order_status | ENUM | - | 是 | 'pending' | 订单状态：pending/paid/shipped/completed/cancelled |
| shipping_address | TEXT | - | 否 | - | 收货地址 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 更新时间 |

### 2.6 订单详情表（order_items）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BIGINT | - | 是 | AUTO_INCREMENT | 详情ID，主键 |
| order_id | BIGINT | - | 是 | - | 订单ID，外键 |
| product_id | INT | - | 是 | - | 商品ID，外键 |
| product_name | VARCHAR | 200 | 是 | - | 商品名称 |
| quantity | INT | - | 是 | - | 购买数量 |
| unit_price | DECIMAL | 10,2 | 是 | - | 单价 |
| total_price | DECIMAL | 10,2 | 是 | - | 小计 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

## 3. API接口设计

### 3.1 用户行为数据采集

**接口地址**: `POST /api/analytics/behavior`

**请求参数**:
```json
{
    "user_id": "int (可选, 用户ID)",
    "session_id": "string (必填, 会话ID)",
    "behavior_type": "string (必填, 行为类型)",
    "target_type": "string (可选, 目标类型)",
    "target_id": "int (可选, 目标ID)",
    "page_url": "string (可选, 页面URL)",
    "referrer_url": "string (可选, 来源URL)",
    "duration": "int (可选, 停留时长)",
    "extra_data": "object (可选, 额外数据)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "数据采集成功",
    "data": {
        "behavior_id": 123456
    }
}
```

### 3.2 页面访问数据采集

**接口地址**: `POST /api/analytics/pageview`

**请求参数**:
```json
{
    "user_id": "int (可选, 用户ID)",
    "session_id": "string (必填, 会话ID)",
    "page_url": "string (必填, 页面URL)",
    "page_title": "string (可选, 页面标题)",
    "referrer_url": "string (可选, 来源URL)",
    "load_time": "int (可选, 加载时间)",
    "stay_time": "int (可选, 停留时间)",
    "scroll_depth": "float (可选, 滚动深度)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "页面访问记录成功",
    "data": {
        "pageview_id": 123456
    }
}
```

### 3.3 搜索行为数据采集

**接口地址**: `POST /api/analytics/search`

**请求参数**:
```json
{
    "user_id": "int (可选, 用户ID)",
    "session_id": "string (必填, 会话ID)",
    "search_keyword": "string (必填, 搜索关键词)",
    "search_type": "string (可选, 搜索类型)",
    "result_count": "int (可选, 结果数量)",
    "click_position": "int (可选, 点击位置)",
    "click_product_id": "int (可选, 点击商品ID)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "搜索记录成功",
    "data": {
        "search_id": 123456
    }
}
```

### 3.4 批量数据采集

**接口地址**: `POST /api/analytics/batch`

**请求参数**:
```json
{
    "events": [
        {
            "type": "behavior",
            "data": {
                "user_id": 1,
                "session_id": "session123",
                "behavior_type": "click",
                "target_type": "product",
                "target_id": 100
            }
        },
        {
            "type": "pageview",
            "data": {
                "session_id": "session123",
                "page_url": "/product/100",
                "stay_time": 60
            }
        }
    ]
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "批量数据处理成功",
    "data": {
        "success_count": 2,
        "failed_count": 0,
        "failed_items": []
    }
}
```

### 3.5 商品管理接口

**接口地址**: `GET /api/products`

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `category_id`: 分类ID筛选
- `search`: 搜索关键词
- `status`: 状态筛选

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "products": [
            {
                "id": 1,
                "name": "iPhone 15",
                "category_id": 1,
                "brand": "Apple",
                "price": 5999.00,
                "original_price": 6999.00,
                "image_url": "http://example.com/iphone15.jpg",
                "stock": 100,
                "status": "active"
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_count": 100,
            "per_page": 20
        }
    }
}
```

### 3.6 创建商品

**接口地址**: `POST /api/products`

**请求头**: `Authorization: Bearer {admin_token}`

**请求参数**:
```json
{
    "name": "string (必填, 商品名称)",
    "category_id": "int (必填, 分类ID)",
    "brand": "string (可选, 品牌)",
    "price": "float (必填, 价格)",
    "original_price": "float (可选, 原价)",
    "description": "string (可选, 描述)",
    "image_url": "string (可选, 图片URL)",
    "stock": "int (必填, 库存)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "商品创建成功",
    "data": {
        "id": 1,
        "name": "iPhone 15",
        "category_id": 1,
        "price": 5999.00,
        "stock": 100
    }
}
```

### 3.7 订单数据接口

**接口地址**: `GET /api/orders`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `user_id`: 用户ID筛选（管理员权限）
- `status`: 订单状态筛选
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "orders": [
            {
                "id": 1,
                "order_no": "ORD20240115001",
                "user_id": 1,
                "total_amount": 5999.00,
                "order_status": "paid",
                "created_at": "2024-01-15 10:30:00",
                "items": [
                    {
                        "product_id": 1,
                        "product_name": "iPhone 15",
                        "quantity": 1,
                        "unit_price": 5999.00
                    }
                ]
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_count": 100,
            "per_page": 20
        }
    }
}
```

### 3.8 数据导入接口

**接口地址**: `POST /api/analytics/import`

**请求头**: `Authorization: Bearer {admin_token}`

**请求参数**: FormData
- `file`: 文件（支持CSV、Excel格式）
- `data_type`: 数据类型（behavior/pageview/search/product/order）
- `mapping`: 字段映射配置（JSON字符串）

**响应数据**:
```json
{
    "code": 200,
    "message": "数据导入成功",
    "data": {
        "total_rows": 1000,
        "success_rows": 950,
        "failed_rows": 50,
        "import_id": "import_123456",
        "errors": [
            {
                "row": 5,
                "error": "缺少必填字段：user_id"
            }
        ]
    }
}
```

### 3.9 实时数据统计

**接口地址**: `GET /api/analytics/realtime`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "online_users": 1250,
        "page_views_today": 15678,
        "orders_today": 234,
        "revenue_today": 123456.78,
        "top_pages": [
            {
                "page_url": "/product/100",
                "views": 567
            }
        ],
        "top_products": [
            {
                "product_id": 100,
                "product_name": "iPhone 15",
                "clicks": 234
            }
        ]
    }
}
```

## 4. 前端集成方案

### 4.1 JavaScript SDK

```javascript
// 初始化埋点SDK
window.AnalyticsSDK = {
    init: function(config) {
        this.apiUrl = config.apiUrl;
        this.sessionId = this.generateSessionId();
        this.userId = config.userId || null;
        this.autoTrack = config.autoTrack || true;
        
        if (this.autoTrack) {
            this.bindAutoEvents();
        }
    },
    
    // 页面浏览埋点
    trackPageView: function(data) {
        this.send('/api/analytics/pageview', {
            session_id: this.sessionId,
            user_id: this.userId,
            page_url: window.location.href,
            page_title: document.title,
            referrer_url: document.referrer,
            ...data
        });
    },
    
    // 用户行为埋点
    trackBehavior: function(behaviorType, data) {
        this.send('/api/analytics/behavior', {
            session_id: this.sessionId,
            user_id: this.userId,
            behavior_type: behaviorType,
            page_url: window.location.href,
            ...data
        });
    },
    
    // 搜索行为埋点
    trackSearch: function(keyword, data) {
        this.send('/api/analytics/search', {
            session_id: this.sessionId,
            user_id: this.userId,
            search_keyword: keyword,
            ...data
        });
    }
};
```

### 4.2 Vue组件埋点

```vue
<template>
  <div>
    <button @click="trackClick">点击按钮</button>
    <input @input="trackSearch" v-model="searchText" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchText: ''
    }
  },
  methods: {
    trackClick() {
      this.$analytics.trackBehavior('click', {
        target_type: 'button',
        target_id: 'submit_btn'
      });
    },
    trackSearch() {
      if (this.searchText) {
        this.$analytics.trackSearch(this.searchText);
      }
    }
  }
}
</script>
```

## 5. 数据验证和清洗

### 5.1 数据验证规则
- Session ID格式验证
- URL格式验证
- 时间戳范围验证
- 必填字段检查
- 数据类型验证

### 5.2 数据清洗规则
- 去除重复数据
- 异常数据过滤
- 数据格式标准化
- 垃圾数据清理

## 6. 性能优化

### 6.1 数据采集优化
- 批量数据提交
- 异步数据处理
- 数据压缩传输
- 本地缓存机制

### 6.2 存储优化
- 数据分表策略
- 索引优化
- 数据归档
- 冷热数据分离
