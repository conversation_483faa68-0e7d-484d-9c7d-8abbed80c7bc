# 系统管理模块详细设计文档

## 1. 模块概述

系统管理模块负责系统的配置管理、数据管理、监控告警、日志管理等功能，为系统的稳定运行和维护提供支持。包括系统配置、数据备份恢复、性能监控、操作日志等子模块。

## 2. 数据库设计

### 2.1 系统配置表（system_configs）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | 是 | AUTO_INCREMENT | 配置ID，主键 |
| config_key | VARCHAR | 100 | 是 | - | 配置键，唯一 |
| config_value | TEXT | - | 是 | - | 配置值 |
| config_type | ENUM | - | 是 | 'string' | 配置类型：string/int/float/bool/json |
| category | VARCHAR | 50 | 是 | - | 配置分类 |
| description | TEXT | - | 否 | - | 配置描述 |
| is_editable | BOOLEAN | - | 是 | TRUE | 是否可编辑 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 更新时间 |

### 2.2 操作日志表（operation_logs）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BIGINT | - | 是 | AUTO_INCREMENT | 日志ID，主键 |
| user_id | INT | - | 否 | - | 操作用户ID |
| username | VARCHAR | 50 | 否 | - | 用户名 |
| operation_type | VARCHAR | 50 | 是 | - | 操作类型 |
| operation_module | VARCHAR | 50 | 是 | - | 操作模块 |
| operation_desc | TEXT | - | 是 | - | 操作描述 |
| request_method | VARCHAR | 10 | 否 | - | 请求方法 |
| request_url | VARCHAR | 500 | 否 | - | 请求URL |
| request_params | JSON | - | 否 | - | 请求参数 |
| response_code | INT | - | 否 | - | 响应状态码 |
| ip_address | VARCHAR | 45 | 否 | - | IP地址 |
| user_agent | TEXT | - | 否 | - | 用户代理 |
| execution_time | INT | - | 否 | - | 执行时间（毫秒） |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

### 2.3 系统监控表（system_monitors）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BIGINT | - | 是 | AUTO_INCREMENT | 监控ID，主键 |
| monitor_type | VARCHAR | 50 | 是 | - | 监控类型 |
| metric_name | VARCHAR | 100 | 是 | - | 指标名称 |
| metric_value | DECIMAL | 15,4 | 是 | - | 指标值 |
| threshold_value | DECIMAL | 15,4 | 否 | - | 阈值 |
| status | ENUM | - | 是 | 'normal' | 状态：normal/warning/critical |
| server_name | VARCHAR | 100 | 否 | - | 服务器名称 |
| metadata | JSON | - | 否 | - | 元数据 |
| collected_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 采集时间 |

### 2.4 数据备份记录表（backup_records）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | 是 | AUTO_INCREMENT | 备份ID，主键 |
| backup_name | VARCHAR | 100 | 是 | - | 备份名称 |
| backup_type | ENUM | - | 是 | - | 备份类型：full/incremental/differential |
| backup_path | VARCHAR | 500 | 是 | - | 备份路径 |
| file_size | BIGINT | - | 是 | - | 文件大小 |
| backup_status | ENUM | - | 是 | 'running' | 状态：running/completed/failed |
| start_time | DATETIME | - | 是 | CURRENT_TIMESTAMP | 开始时间 |
| end_time | DATETIME | - | 否 | - | 结束时间 |
| error_message | TEXT | - | 否 | - | 错误信息 |
| created_by | INT | - | 是 | - | 创建人ID |

### 2.5 系统告警表（system_alerts）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BIGINT | - | 是 | AUTO_INCREMENT | 告警ID，主键 |
| alert_type | VARCHAR | 50 | 是 | - | 告警类型 |
| alert_level | ENUM | - | 是 | - | 告警级别：info/warning/error/critical |
| alert_title | VARCHAR | 200 | 是 | - | 告警标题 |
| alert_content | TEXT | - | 是 | - | 告警内容 |
| alert_source | VARCHAR | 100 | 否 | - | 告警来源 |
| is_read | BOOLEAN | - | 是 | FALSE | 是否已读 |
| is_handled | BOOLEAN | - | 是 | FALSE | 是否已处理 |
| handled_by | INT | - | 否 | - | 处理人ID |
| handled_at | DATETIME | - | 否 | - | 处理时间 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

## 3. API接口设计

### 3.1 系统配置管理

#### 3.1.1 获取系统配置列表

**接口地址**: `GET /api/system/configs`

**请求头**: `Authorization: Bearer {admin_token}`

**查询参数**:
- `category`: 配置分类筛选
- `search`: 搜索关键词

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "configs": [
            {
                "id": 1,
                "config_key": "data_retention_days",
                "config_value": "365",
                "config_type": "int",
                "category": "data_management",
                "description": "数据保留天数",
                "is_editable": true
            },
            {
                "id": 2,
                "config_key": "email_smtp_server",
                "config_value": "smtp.example.com",
                "config_type": "string",
                "category": "email",
                "description": "邮件SMTP服务器",
                "is_editable": true
            }
        ],
        "categories": [
            {
                "name": "data_management",
                "label": "数据管理",
                "count": 5
            },
            {
                "name": "email",
                "label": "邮件配置",
                "count": 3
            }
        ]
    }
}
```

#### 3.1.2 更新系统配置

**接口地址**: `PUT /api/system/configs/{config_id}`

**请求头**: `Authorization: Bearer {admin_token}`

**请求参数**:
```json
{
    "config_value": "string (必填, 配置值)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "配置更新成功",
    "data": {
        "id": 1,
        "config_key": "data_retention_days",
        "config_value": "180",
        "updated_at": "2024-01-15 10:30:00"
    }
}
```

#### 3.1.3 批量更新配置

**接口地址**: `POST /api/system/configs/batch-update`

**请求头**: `Authorization: Bearer {admin_token}`

**请求参数**:
```json
{
    "configs": [
        {
            "config_key": "data_retention_days",
            "config_value": "180"
        },
        {
            "config_key": "email_smtp_port",
            "config_value": "587"
        }
    ]
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "批量更新成功",
    "data": {
        "updated_count": 2,
        "failed_count": 0
    }
}
```

### 3.2 操作日志管理

#### 3.2.1 获取操作日志列表

**接口地址**: `GET /api/system/operation-logs`

**请求头**: `Authorization: Bearer {admin_token}`

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `user_id`: 用户ID筛选
- `operation_type`: 操作类型筛选
- `operation_module`: 操作模块筛选
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "logs": [
            {
                "id": 12345,
                "user_id": 1,
                "username": "admin",
                "operation_type": "update",
                "operation_module": "user_management",
                "operation_desc": "更新用户信息",
                "request_method": "PUT",
                "request_url": "/api/users/1",
                "response_code": 200,
                "ip_address": "*************",
                "execution_time": 150,
                "created_at": "2024-01-15 10:30:00"
            }
        ],
        "summary": {
            "total_operations": 50000,
            "today_operations": 1200,
            "error_rate": 0.05
        },
        "pagination": {
            "current_page": 1,
            "total_pages": 1000,
            "total_count": 50000,
            "per_page": 20
        }
    }
}
```

#### 3.2.2 获取操作统计

**接口地址**: `GET /api/system/operation-logs/statistics`

**请求头**: `Authorization: Bearer {admin_token}`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期
- `group_by`: 分组方式 (user/module/type/hour/day)

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "operation_trend": [
            {
                "date": "2024-01-15",
                "total_count": 1500,
                "success_count": 1425,
                "error_count": 75
            }
        ],
        "top_users": [
            {
                "user_id": 1,
                "username": "admin",
                "operation_count": 500,
                "error_rate": 0.02
            }
        ],
        "module_distribution": [
            {
                "module": "user_management",
                "count": 800,
                "percentage": 53.3
            },
            {
                "module": "data_analysis",
                "count": 400,
                "percentage": 26.7
            }
        ]
    }
}
```

### 3.3 系统监控

#### 3.3.1 获取系统监控指标

**接口地址**: `GET /api/system/monitors/metrics`

**请求头**: `Authorization: Bearer {admin_token}`

**查询参数**:
- `monitor_type`: 监控类型 (server/database/application)
- `time_range`: 时间范围 (1h/6h/24h/7d)

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "server_metrics": {
            "cpu_usage": {
                "current": 65.5,
                "threshold": 80.0,
                "status": "normal",
                "trend": [
                    {"time": "10:00", "value": 60.2},
                    {"time": "10:05", "value": 62.1},
                    {"time": "10:10", "value": 65.5}
                ]
            },
            "memory_usage": {
                "current": 72.3,
                "threshold": 85.0,
                "status": "normal",
                "trend": [
                    {"time": "10:00", "value": 70.1},
                    {"time": "10:05", "value": 71.2},
                    {"time": "10:10", "value": 72.3}
                ]
            },
            "disk_usage": {
                "current": 45.8,
                "threshold": 90.0,
                "status": "normal"
            }
        },
        "database_metrics": {
            "connection_count": 25,
            "query_per_second": 150.5,
            "slow_query_count": 2,
            "status": "normal"
        },
        "application_metrics": {
            "active_users": 1250,
            "request_per_minute": 3500,
            "error_rate": 0.02,
            "response_time": 285
        }
    }
}
```

#### 3.3.2 获取系统健康状态

**接口地址**: `GET /api/system/health`

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "overall_status": "healthy",
        "services": [
            {
                "name": "Database",
                "status": "healthy",
                "response_time": 15,
                "last_check": "2024-01-15 10:30:00"
            },
            {
                "name": "Redis",
                "status": "healthy",
                "response_time": 2,
                "last_check": "2024-01-15 10:30:00"
            },
            {
                "name": "Email Service",
                "status": "warning",
                "response_time": 5000,
                "last_check": "2024-01-15 10:30:00",
                "message": "响应时间较慢"
            }
        ],
        "system_info": {
            "version": "1.0.0",
            "uptime": "15天3小时25分钟",
            "last_restart": "2024-01-01 06:00:00"
        }
    }
}
```

### 3.4 数据管理

#### 3.4.1 创建数据备份

**接口地址**: `POST /api/system/backup`

**请求头**: `Authorization: Bearer {admin_token}`

**请求参数**:
```json
{
    "backup_name": "string (可选, 备份名称)",
    "backup_type": "string (必填, 备份类型: full/incremental)",
    "tables": "array (可选, 指定表名)",
    "description": "string (可选, 备份描述)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "备份任务已创建",
    "data": {
        "backup_id": 123,
        "backup_name": "backup_20240115_103000",
        "status": "running",
        "estimated_time": 600
    }
}
```

#### 3.4.2 获取备份列表

**接口地址**: `GET /api/system/backups`

**请求头**: `Authorization: Bearer {admin_token}`

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `backup_type`: 备份类型筛选
- `status`: 状态筛选

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "backups": [
            {
                "id": 123,
                "backup_name": "backup_20240115_103000",
                "backup_type": "full",
                "file_size": 524288000,
                "backup_status": "completed",
                "start_time": "2024-01-15 10:30:00",
                "end_time": "2024-01-15 10:40:00",
                "duration": 600,
                "download_url": "/api/system/backups/123/download"
            }
        ],
        "summary": {
            "total_backups": 50,
            "total_size": 10737418240,
            "last_backup": "2024-01-15 10:30:00"
        },
        "pagination": {
            "current_page": 1,
            "total_pages": 3,
            "total_count": 50,
            "per_page": 20
        }
    }
}
```

#### 3.4.3 恢复数据

**接口地址**: `POST /api/system/restore`

**请求头**: `Authorization: Bearer {admin_token}`

**请求参数**:
```json
{
    "backup_id": "int (必填, 备份ID)",
    "restore_tables": "array (可选, 指定恢复的表)",
    "force_restore": "boolean (可选, 强制恢复)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "恢复任务已创建",
    "data": {
        "restore_id": 456,
        "status": "running",
        "estimated_time": 300
    }
}
```

#### 3.4.4 数据清理

**接口地址**: `POST /api/system/data-cleanup`

**请求头**: `Authorization: Bearer {admin_token}`

**请求参数**:
```json
{
    "cleanup_type": "string (必填, 清理类型: expired_logs/old_reports/temp_files)",
    "days_ago": "int (必填, 清理多少天前的数据)",
    "confirm": "boolean (必填, 确认清理)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "数据清理完成",
    "data": {
        "cleanup_type": "expired_logs",
        "deleted_count": 50000,
        "freed_space": 1073741824,
        "execution_time": 120
    }
}
```

### 3.5 系统告警

#### 3.5.1 获取告警列表

**接口地址**: `GET /api/system/alerts`

**请求头**: `Authorization: Bearer {admin_token}`

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `alert_level`: 告警级别筛选
- `is_read`: 是否已读筛选
- `is_handled`: 是否已处理筛选

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "alerts": [
            {
                "id": 789,
                "alert_type": "system_performance",
                "alert_level": "warning",
                "alert_title": "CPU使用率过高",
                "alert_content": "服务器CPU使用率达到85%，超过预设阈值80%",
                "alert_source": "server_monitor",
                "is_read": false,
                "is_handled": false,
                "created_at": "2024-01-15 10:30:00"
            }
        ],
        "summary": {
            "total_alerts": 150,
            "unread_count": 25,
            "unhandled_count": 18,
            "critical_count": 3
        },
        "pagination": {
            "current_page": 1,
            "total_pages": 8,
            "total_count": 150,
            "per_page": 20
        }
    }
}
```

#### 3.5.2 处理告警

**接口地址**: `PUT /api/system/alerts/{alert_id}/handle`

**请求头**: `Authorization: Bearer {admin_token}`

**请求参数**:
```json
{
    "handle_note": "string (可选, 处理备注)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "告警已处理",
    "data": {
        "alert_id": 789,
        "handled_by": 1,
        "handled_at": "2024-01-15 11:00:00"
    }
}
```

#### 3.5.3 批量标记已读

**接口地址**: `POST /api/system/alerts/mark-read`

**请求头**: `Authorization: Bearer {admin_token}`

**请求参数**:
```json
{
    "alert_ids": "array (必填, 告警ID列表)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "批量标记成功",
    "data": {
        "updated_count": 10
    }
}
```

## 4. 系统监控实现

### 4.1 性能监控脚本

```python
# system_monitor.py
import psutil
import pymysql
import redis
import time
from datetime import datetime
from django.db import connection

class SystemMonitor:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        
    def collect_server_metrics(self):
        """收集服务器指标"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        metrics = {
            'cpu_usage': cpu_percent,
            'memory_usage': memory.percent,
            'memory_available': memory.available,
            'disk_usage': (disk.used / disk.total) * 100,
            'disk_free': disk.free,
            'load_average': psutil.getloadavg()[0] if hasattr(psutil, 'getloadavg') else 0
        }
        
        # 保存到数据库
        self._save_metrics('server', metrics)
        
        return metrics
    
    def collect_database_metrics(self):
        """收集数据库指标"""
        try:
            with connection.cursor() as cursor:
                # 查询连接数
                cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
                connections = cursor.fetchone()[1]
                
                # 查询QPS
                cursor.execute("SHOW STATUS LIKE 'Questions'")
                questions = cursor.fetchone()[1]
                
                # 查询慢查询
                cursor.execute("SHOW STATUS LIKE 'Slow_queries'")
                slow_queries = cursor.fetchone()[1]
                
                metrics = {
                    'connection_count': int(connections),
                    'total_questions': int(questions),
                    'slow_query_count': int(slow_queries)
                }
                
                self._save_metrics('database', metrics)
                return metrics
                
        except Exception as e:
            print(f"数据库监控异常: {e}")
            return {}
    
    def collect_application_metrics(self):
        """收集应用指标"""
        try:
            # 获取在线用户数
            online_users = self.redis_client.get('online_users_count') or 0
            
            # 获取今日PV
            today_pv = self.redis_client.get('today_pv_count') or 0
            
            # 获取错误率
            error_count = self.redis_client.get('error_count_1h') or 0
            request_count = self.redis_client.get('request_count_1h') or 1
            error_rate = int(error_count) / int(request_count)
            
            metrics = {
                'online_users': int(online_users),
                'today_pv': int(today_pv),
                'error_rate': error_rate,
                'request_count_1h': int(request_count)
            }
            
            self._save_metrics('application', metrics)
            return metrics
            
        except Exception as e:
            print(f"应用监控异常: {e}")
            return {}
    
    def _save_metrics(self, monitor_type, metrics):
        """保存指标到数据库"""
        from .models import SystemMonitor as Monitor
        
        for metric_name, metric_value in metrics.items():
            Monitor.objects.create(
                monitor_type=monitor_type,
                metric_name=metric_name,
                metric_value=metric_value,
                collected_at=datetime.now()
            )
    
    def check_thresholds(self):
        """检查阈值并生成告警"""
        from .models import SystemConfig, SystemAlert
        
        # 获取阈值配置
        thresholds = {
            'cpu_usage_threshold': 80.0,
            'memory_usage_threshold': 85.0,
            'disk_usage_threshold': 90.0,
            'error_rate_threshold': 0.05
        }
        
        # 更新阈值配置
        for key in thresholds:
            config = SystemConfig.objects.filter(config_key=key).first()
            if config:
                thresholds[key] = float(config.config_value)
        
        # 检查最新指标
        latest_metrics = self._get_latest_metrics()
        
        for metric_name, value in latest_metrics.items():
            threshold_key = f"{metric_name}_threshold"
            if threshold_key in thresholds:
                threshold = thresholds[threshold_key]
                
                if value > threshold:
                    self._create_alert(
                        alert_type='system_performance',
                        alert_level='warning' if value < threshold * 1.2 else 'critical',
                        alert_title=f'{metric_name}超过阈值',
                        alert_content=f'{metric_name}当前值为{value:.2f}，超过阈值{threshold}',
                        alert_source='system_monitor'
                    )
    
    def _get_latest_metrics(self):
        """获取最新指标"""
        from .models import SystemMonitor as Monitor
        
        latest_metrics = {}
        
        # 获取最近1分钟的指标
        one_minute_ago = datetime.now() - timedelta(minutes=1)
        monitors = Monitor.objects.filter(
            collected_at__gte=one_minute_ago
        ).values('metric_name').annotate(
            avg_value=models.Avg('metric_value')
        )
        
        for monitor in monitors:
            latest_metrics[monitor['metric_name']] = monitor['avg_value']
        
        return latest_metrics
    
    def _create_alert(self, alert_type, alert_level, alert_title, alert_content, alert_source):
        """创建告警"""
        from .models import SystemAlert
        
        # 检查是否已存在相同告警（防止重复告警）
        existing_alert = SystemAlert.objects.filter(
            alert_type=alert_type,
            alert_title=alert_title,
            is_handled=False,
            created_at__gte=datetime.now() - timedelta(minutes=30)
        ).first()
        
        if not existing_alert:
            SystemAlert.objects.create(
                alert_type=alert_type,
                alert_level=alert_level,
                alert_title=alert_title,
                alert_content=alert_content,
                alert_source=alert_source
            )
```

### 4.2 Celery定时监控任务

```python
# monitor_tasks.py
from celery import Celery
from celery.schedules import crontab
from .system_monitor import SystemMonitor

app = Celery('monitor_tasks')

@app.task
def collect_system_metrics():
    """收集系统指标"""
    monitor = SystemMonitor()
    
    # 收集各类指标
    server_metrics = monitor.collect_server_metrics()
    db_metrics = monitor.collect_database_metrics()
    app_metrics = monitor.collect_application_metrics()
    
    # 检查阈值
    monitor.check_thresholds()
    
    return {
        'server': server_metrics,
        'database': db_metrics,
        'application': app_metrics,
        'timestamp': datetime.now().isoformat()
    }

@app.task
def cleanup_old_monitors():
    """清理过期的监控数据"""
    from .models import SystemMonitor
    from datetime import datetime, timedelta
    
    # 删除30天前的监控数据
    cutoff_date = datetime.now() - timedelta(days=30)
    deleted_count = SystemMonitor.objects.filter(
        collected_at__lt=cutoff_date
    ).delete()[0]
    
    return f"清理了 {deleted_count} 条过期监控数据"

@app.task
def generate_system_report():
    """生成系统运行报告"""
    from .models import SystemMonitor, OperationLog
    from datetime import datetime, timedelta
    
    yesterday = datetime.now().date() - timedelta(days=1)
    
    # 统计昨日系统指标
    daily_stats = SystemMonitor.objects.filter(
        collected_at__date=yesterday
    ).values('metric_name').annotate(
        avg_value=models.Avg('metric_value'),
        max_value=models.Max('metric_value'),
        min_value=models.Min('metric_value')
    )
    
    # 统计昨日操作日志
    operation_stats = OperationLog.objects.filter(
        created_at__date=yesterday
    ).aggregate(
        total_operations=models.Count('id'),
        error_operations=models.Count('id', filter=models.Q(response_code__gte=400)),
        avg_execution_time=models.Avg('execution_time')
    )
    
    # 生成报告
    report = {
        'date': yesterday.isoformat(),
        'system_metrics': list(daily_stats),
        'operation_stats': operation_stats,
        'generated_at': datetime.now().isoformat()
    }
    
    # 可以发送邮件或保存到文件
    return report

# 定时任务配置
app.conf.beat_schedule = {
    'collect-system-metrics': {
        'task': 'collect_system_metrics',
        'schedule': 60.0,  # 每分钟执行
    },
    'cleanup-old-monitors': {
        'task': 'cleanup_old_monitors',
        'schedule': crontab(hour=2, minute=0),  # 每天凌晨2点执行
    },
    'generate-system-report': {
        'task': 'generate_system_report',
        'schedule': crontab(hour=6, minute=0),  # 每天早上6点生成报告
    },
}
```

## 5. 数据备份恢复

### 5.1 备份脚本

```python
# backup_manager.py
import os
import subprocess
import gzip
import shutil
from datetime import datetime
from django.conf import settings

class BackupManager:
    def __init__(self):
        self.backup_dir = settings.BACKUP_DIR
        self.db_config = settings.DATABASES['default']
        
    def create_full_backup(self, backup_name=None):
        """创建全量备份"""
        if not backup_name:
            backup_name = f"full_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_path = os.path.join(self.backup_dir, f"{backup_name}.sql.gz")
        
        try:
            # 构建mysqldump命令
            cmd = [
                'mysqldump',
                f"--host={self.db_config['HOST']}",
                f"--port={self.db_config['PORT']}",
                f"--user={self.db_config['USER']}",
                f"--password={self.db_config['PASSWORD']}",
                '--single-transaction',
                '--routines',
                '--triggers',
                self.db_config['NAME']
            ]
            
            # 执行备份
            with open(backup_path.replace('.gz', ''), 'w') as f:
                subprocess.run(cmd, stdout=f, check=True)
            
            # 压缩备份文件
            with open(backup_path.replace('.gz', ''), 'rb') as f_in:
                with gzip.open(backup_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # 删除未压缩文件
            os.remove(backup_path.replace('.gz', ''))
            
            # 获取文件大小
            file_size = os.path.getsize(backup_path)
            
            return {
                'backup_name': backup_name,
                'backup_path': backup_path,
                'file_size': file_size,
                'status': 'completed'
            }
            
        except subprocess.CalledProcessError as e:
            return {
                'backup_name': backup_name,
                'status': 'failed',
                'error': str(e)
            }
    
    def create_incremental_backup(self, last_backup_time):
        """创建增量备份"""
        backup_name = f"incremental_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_path = os.path.join(self.backup_dir, f"{backup_name}.sql.gz")
        
        try:
            # 构建增量备份的查询条件
            where_clause = f"WHERE updated_at > '{last_backup_time}'"
            
            # 这里可以实现更复杂的增量备份逻辑
            # 例如只备份特定表的变更数据
            
            cmd = [
                'mysqldump',
                f"--host={self.db_config['HOST']}",
                f"--port={self.db_config['PORT']}",
                f"--user={self.db_config['USER']}",
                f"--password={self.db_config['PASSWORD']}",
                '--single-transaction',
                f"--where={where_clause}",
                self.db_config['NAME']
            ]
            
            with open(backup_path.replace('.gz', ''), 'w') as f:
                subprocess.run(cmd, stdout=f, check=True)
            
            # 压缩
            with open(backup_path.replace('.gz', ''), 'rb') as f_in:
                with gzip.open(backup_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            os.remove(backup_path.replace('.gz', ''))
            
            file_size = os.path.getsize(backup_path)
            
            return {
                'backup_name': backup_name,
                'backup_path': backup_path,
                'file_size': file_size,
                'status': 'completed'
            }
            
        except subprocess.CalledProcessError as e:
            return {
                'backup_name': backup_name,
                'status': 'failed',
                'error': str(e)
            }
    
    def restore_backup(self, backup_path):
        """恢复备份"""
        try:
            # 解压备份文件
            sql_file = backup_path.replace('.gz', '')
            
            with gzip.open(backup_path, 'rb') as f_in:
                with open(sql_file, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # 构建mysql恢复命令
            cmd = [
                'mysql',
                f"--host={self.db_config['HOST']}",
                f"--port={self.db_config['PORT']}",
                f"--user={self.db_config['USER']}",
                f"--password={self.db_config['PASSWORD']}",
                self.db_config['NAME']
            ]
            
            # 执行恢复
            with open(sql_file, 'r') as f:
                subprocess.run(cmd, stdin=f, check=True)
            
            # 清理临时文件
            os.remove(sql_file)
            
            return {'status': 'completed'}
            
        except subprocess.CalledProcessError as e:
            return {'status': 'failed', 'error': str(e)}
    
    def cleanup_old_backups(self, retention_days=30):
        """清理过期备份"""
        cutoff_time = datetime.now().timestamp() - (retention_days * 24 * 3600)
        deleted_files = []
        
        for filename in os.listdir(self.backup_dir):
            file_path = os.path.join(self.backup_dir, filename)
            if os.path.isfile(file_path):
                file_time = os.path.getmtime(file_path)
                if file_time < cutoff_time:
                    os.remove(file_path)
                    deleted_files.append(filename)
        
        return {
            'deleted_count': len(deleted_files),
            'deleted_files': deleted_files
        }
```

## 6. 前端系统管理界面

### 6.1 系统监控大屏

```vue
<!-- SystemMonitor.vue -->
<template>
  <div class="system-monitor">
    <div class="monitor-header">
      <h2>系统监控大屏</h2>
      <div class="last-update">
        最后更新: {{ lastUpdateTime }}
      </div>
    </div>

    <div class="monitor-grid">
      <!-- 系统概览 -->
      <div class="monitor-card overview">
        <h3>系统概览</h3>
        <div class="overview-metrics">
          <div class="metric-item">
            <div class="metric-label">系统状态</div>
            <div :class="['metric-value', 'status', systemStatus]">
              {{ systemStatusText }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">运行时间</div>
            <div class="metric-value">{{ uptime }}</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">在线用户</div>
            <div class="metric-value">{{ onlineUsers }}</div>
          </div>
        </div>
      </div>

      <!-- CPU使用率 -->
      <div class="monitor-card">
        <h3>CPU使用率</h3>
        <div class="chart-container">
          <line-chart
            :data="cpuChartData"
            :height="200"
            :options="chartOptions"
          />
        </div>
        <div class="current-value">
          当前: {{ currentMetrics.cpu_usage }}%
        </div>
      </div>

      <!-- 内存使用率 -->
      <div class="monitor-card">
        <h3>内存使用率</h3>
        <div class="chart-container">
          <line-chart
            :data="memoryChartData"
            :height="200"
            :options="chartOptions"
          />
        </div>
        <div class="current-value">
          当前: {{ currentMetrics.memory_usage }}%
        </div>
      </div>

      <!-- 磁盘使用率 -->
      <div class="monitor-card">
        <h3>磁盘使用率</h3>
        <div class="progress-container">
          <el-progress
            :percentage="currentMetrics.disk_usage"
            :color="getDiskColor(currentMetrics.disk_usage)"
            :stroke-width="20"
          />
        </div>
        <div class="disk-info">
          已用: {{ formatBytes(diskInfo.used) }} / 
          总计: {{ formatBytes(diskInfo.total) }}
        </div>
      </div>

      <!-- 数据库状态 -->
      <div class="monitor-card">
        <h3>数据库状态</h3>
        <div class="db-metrics">
          <div class="db-metric">
            <span class="label">连接数:</span>
            <span class="value">{{ dbMetrics.connection_count }}</span>
          </div>
          <div class="db-metric">
            <span class="label">QPS:</span>
            <span class="value">{{ dbMetrics.query_per_second }}</span>
          </div>
          <div class="db-metric">
            <span class="label">慢查询:</span>
            <span class="value">{{ dbMetrics.slow_query_count }}</span>
          </div>
        </div>
      </div>

      <!-- 告警信息 -->
      <div class="monitor-card alerts">
        <h3>最新告警</h3>
        <div class="alert-list">
          <div
            v-for="alert in recentAlerts"
            :key="alert.id"
            :class="['alert-item', alert.alert_level]"
          >
            <div class="alert-title">{{ alert.alert_title }}</div>
            <div class="alert-time">{{ formatTime(alert.created_at) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import LineChart from '@/components/charts/LineChart.vue'
import { getSystemMetrics, getSystemAlerts } from '@/api/system'

export default {
  name: 'SystemMonitor',
  components: {
    LineChart
  },
  setup() {
    const lastUpdateTime = ref('')
    const systemStatus = ref('healthy')
    const systemStatusText = ref('正常')
    const uptime = ref('0天0小时0分钟')
    const onlineUsers = ref(0)
    
    const currentMetrics = reactive({
      cpu_usage: 0,
      memory_usage: 0,
      disk_usage: 0
    })
    
    const diskInfo = reactive({
      used: 0,
      total: 0
    })
    
    const dbMetrics = reactive({
      connection_count: 0,
      query_per_second: 0,
      slow_query_count: 0
    })
    
    const recentAlerts = ref([])
    const cpuChartData = ref({ series: [], xAxis: [] })
    const memoryChartData = ref({ series: [], xAxis: [] })
    
    const chartOptions = {
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}%'
      },
      grid: {
        top: 30,
        left: 40,
        right: 30,
        bottom: 30
      }
    }
    
    let intervalId = null

    const loadSystemMetrics = async () => {
      try {
        const response = await getSystemMetrics({
          time_range: '1h'
        })
        
        const data = response.data
        
        // 更新当前指标
        Object.assign(currentMetrics, {
          cpu_usage: data.server_metrics.cpu_usage.current,
          memory_usage: data.server_metrics.memory_usage.current,
          disk_usage: data.server_metrics.disk_usage.current
        })
        
        // 更新数据库指标
        Object.assign(dbMetrics, data.database_metrics)
        
        // 更新应用指标
        onlineUsers.value = data.application_metrics.active_users
        
        // 更新图表数据
        cpuChartData.value = {
          series: [{
            name: 'CPU使用率',
            data: data.server_metrics.cpu_usage.trend.map(item => item.value),
            type: 'line',
            smooth: true,
            itemStyle: { color: '#1890ff' }
          }],
          xAxis: data.server_metrics.cpu_usage.trend.map(item => item.time)
        }
        
        memoryChartData.value = {
          series: [{
            name: '内存使用率',
            data: data.server_metrics.memory_usage.trend.map(item => item.value),
            type: 'line',
            smooth: true,
            itemStyle: { color: '#52c41a' }
          }],
          xAxis: data.server_metrics.memory_usage.trend.map(item => item.time)
        }
        
        lastUpdateTime.value = new Date().toLocaleString()
        
      } catch (error) {
        console.error('加载系统指标失败:', error)
      }
    }

    const loadSystemAlerts = async () => {
      try {
        const response = await getSystemAlerts({
          limit: 5,
          is_handled: false
        })
        
        recentAlerts.value = response.data.alerts
      } catch (error) {
        console.error('加载系统告警失败:', error)
      }
    }

    const getDiskColor = (percentage) => {
      if (percentage < 70) return '#67C23A'
      if (percentage < 85) return '#E6A23C'
      return '#F56C6C'
    }

    const formatBytes = (bytes) => {
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
      if (bytes === 0) return '0 Bytes'
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    }

    const formatTime = (timeStr) => {
      return new Date(timeStr).toLocaleString()
    }

    onMounted(() => {
      loadSystemMetrics()
      loadSystemAlerts()
      
      // 设置定时刷新
      intervalId = setInterval(() => {
        loadSystemMetrics()
        loadSystemAlerts()
      }, 30000) // 30秒刷新一次
    })

    onUnmounted(() => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    })

    return {
      lastUpdateTime,
      systemStatus,
      systemStatusText,
      uptime,
      onlineUsers,
      currentMetrics,
      diskInfo,
      dbMetrics,
      recentAlerts,
      cpuChartData,
      memoryChartData,
      chartOptions,
      getDiskColor,
      formatBytes,
      formatTime
    }
  }
}
</script>

<style scoped>
.system-monitor {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.monitor-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.monitor-card h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
}

.overview-metrics {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.metric-item {
  text-align: center;
  flex: 1;
}

.metric-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.metric-value.status.healthy {
  color: #52c41a;
}

.metric-value.status.warning {
  color: #fa8c16;
}

.metric-value.status.error {
  color: #f5222d;
}

.current-value {
  text-align: center;
  margin-top: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.progress-container {
  margin: 20px 0;
}

.disk-info {
  text-align: center;
  font-size: 12px;
  color: #666;
}

.db-metrics {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.db-metric {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.db-metric:last-child {
  border-bottom: none;
}

.alert-list {
  max-height: 200px;
  overflow-y: auto;
}

.alert-item {
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  border-left: 4px solid;
}

.alert-item.warning {
  background: #fff7e6;
  border-left-color: #fa8c16;
}

.alert-item.error {
  background: #fff2f0;
  border-left-color: #f5222d;
}

.alert-item.critical {
  background: #fff0f6;
  border-left-color: #eb2f96;
}

.alert-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 12px;
  color: #666;
}
</style>
```

## 7. 安全和权限控制

### 7.1 操作日志记录装饰器

```python
# log_decorator.py
import json
import time
from functools import wraps
from django.http import JsonResponse
from .models import OperationLog

def log_operation(operation_type, operation_module, operation_desc):
    """操作日志记录装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            start_time = time.time()
            
            # 获取请求信息
            user = getattr(request, 'user', None)
            user_id = user.id if user and user.is_authenticated else None
            username = user.username if user and user.is_authenticated else 'anonymous'
            
            ip_address = get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            
            # 获取请求参数
            request_params = {}
            if request.method == 'GET':
                request_params = dict(request.GET)
            elif request.method in ['POST', 'PUT', 'PATCH']:
                try:
                    request_params = json.loads(request.body.decode('utf-8'))
                except:
                    request_params = dict(request.POST)
            
            try:
                # 执行原函数
                response = func(request, *args, **kwargs)
                
                # 计算执行时间
                execution_time = int((time.time() - start_time) * 1000)
                
                # 获取响应状态码
                if isinstance(response, JsonResponse):
                    response_code = response.status_code
                else:
                    response_code = 200
                
                # 记录操作日志
                OperationLog.objects.create(
                    user_id=user_id,
                    username=username,
                    operation_type=operation_type,
                    operation_module=operation_module,
                    operation_desc=operation_desc,
                    request_method=request.method,
                    request_url=request.get_full_path(),
                    request_params=request_params,
                    response_code=response_code,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    execution_time=execution_time
                )
                
                return response
                
            except Exception as e:
                # 记录异常日志
                execution_time = int((time.time() - start_time) * 1000)
                
                OperationLog.objects.create(
                    user_id=user_id,
                    username=username,
                    operation_type=operation_type,
                    operation_module=operation_module,
                    operation_desc=f"{operation_desc} - 执行失败",
                    request_method=request.method,
                    request_url=request.get_full_path(),
                    request_params=request_params,
                    response_code=500,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    execution_time=execution_time
                )
                
                raise e
        
        return wrapper
    return decorator

def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip
```

### 7.2 权限验证装饰器

```python
# permission_decorator.py
from functools import wraps
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required

def admin_required(func):
    """管理员权限验证装饰器"""
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return JsonResponse({
                'code': 401,
                'message': '请先登录'
            }, status=401)
        
        if request.user.role != 'admin':
            return JsonResponse({
                'code': 403,
                'message': '权限不足，需要管理员权限'
            }, status=403)
        
        return func(request, *args, **kwargs)
    
    return wrapper

def permission_required(permissions):
    """权限验证装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return JsonResponse({
                    'code': 401,
                    'message': '请先登录'
                }, status=401)
            
            user_permissions = get_user_permissions(request.user)
            
            if isinstance(permissions, str):
                permissions_list = [permissions]
            else:
                permissions_list = permissions
            
            for permission in permissions_list:
                if permission not in user_permissions:
                    return JsonResponse({
                        'code': 403,
                        'message': f'权限不足，需要{permission}权限'
                    }, status=403)
            
            return func(request, *args, **kwargs)
        
        return wrapper
    return decorator

def get_user_permissions(user):
    """获取用户权限列表"""
    # 这里可以根据用户角色返回对应的权限列表
    if user.role == 'admin':
        return [
            'system.config.read',
            'system.config.write',
            'system.monitor.read',
            'system.backup.create',
            'system.backup.restore',
            'user.manage',
            'report.manage'
        ]
    elif user.role == 'user':
        return [
            'dashboard.read',
            'report.read',
            'analysis.read'
        ]
    else:
        return []
```

## 8. 性能优化

### 8.1 数据库优化

```sql
-- 为操作日志表创建索引
CREATE INDEX idx_operation_logs_user_id ON operation_logs(user_id);
CREATE INDEX idx_operation_logs_created_at ON operation_logs(created_at);
CREATE INDEX idx_operation_logs_operation_type ON operation_logs(operation_type);
CREATE INDEX idx_operation_logs_ip_address ON operation_logs(ip_address);

-- 为系统监控表创建索引
CREATE INDEX idx_system_monitors_type_name ON system_monitors(monitor_type, metric_name);
CREATE INDEX idx_system_monitors_collected_at ON system_monitors(collected_at);
CREATE INDEX idx_system_monitors_status ON system_monitors(status);

-- 为系统告警表创建索引
CREATE INDEX idx_system_alerts_level ON system_alerts(alert_level);
CREATE INDEX idx_system_alerts_created_at ON system_alerts(created_at);
CREATE INDEX idx_system_alerts_is_handled ON system_alerts(is_handled);

-- 创建分区表（按月分区）
ALTER TABLE operation_logs PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404)
);
```

### 8.2 缓存策略

```python
# cache_manager.py
import redis
from django.core.cache import cache
from django.conf import settings

class SystemCacheManager:
    def __init__(self):
        self.redis_client = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB
        )
        
    def cache_system_metrics(self, metrics, cache_time=300):
        """缓存系统指标"""
        cache_key = 'system_metrics_latest'
        cache.set(cache_key, metrics, cache_time)
        
    def get_cached_metrics(self):
        """获取缓存的系统指标"""
        cache_key = 'system_metrics_latest'
        return cache.get(cache_key)
        
    def cache_alert_count(self, count, cache_time=60):
        """缓存告警数量"""
        cache_key = 'system_alert_count'
        cache.set(cache_key, count, cache_time)
        
    def get_cached_alert_count(self):
        """获取缓存的告警数量"""
        cache_key = 'system_alert_count'
        return cache.get(cache_key, 0)
        
    def invalidate_cache(self, pattern):
        """清除匹配模式的缓存"""
        keys = self.redis_client.keys(f"{settings.CACHE_KEY_PREFIX}*{pattern}*")
        if keys:
            self.redis_client.delete(*keys)
```

现在我已经完成了系统管理模块的详细设计文档，让我更新最后的todo状态：

<function_calls>
<invoke name="todo_write">
<parameter name="merge">true
