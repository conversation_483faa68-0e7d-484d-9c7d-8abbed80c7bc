# 数据可视化模块详细设计文档

## 1. 模块概述

数据可视化模块负责将分析结果以图表、大屏、地图等形式进行可视化展示，为用户提供直观的数据洞察。包括实时数据大屏、分析图表、地图可视化等功能。

## 2. 数据库设计

### 2.1 仪表盘配置表（dashboard_configs）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | 是 | AUTO_INCREMENT | 配置ID，主键 |
| name | VARCHAR | 100 | 是 | - | 仪表盘名称 |
| description | TEXT | - | 否 | - | 描述 |
| layout_config | JSON | - | 是 | - | 布局配置 |
| widgets | JSON | - | 是 | - | 组件配置 |
| is_public | BOOLEAN | - | 是 | FALSE | 是否公开 |
| created_by | INT | - | 是 | - | 创建人ID |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 更新时间 |

### 2.2 图表配置表（chart_configs）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | 是 | AUTO_INCREMENT | 图表ID，主键 |
| chart_name | VARCHAR | 100 | 是 | - | 图表名称 |
| chart_type | ENUM | - | 是 | - | 图表类型：line/bar/pie/scatter/heatmap |
| data_source | VARCHAR | 100 | 是 | - | 数据源 |
| query_config | JSON | - | 是 | - | 查询配置 |
| style_config | JSON | - | 是 | - | 样式配置 |
| refresh_interval | INT | - | 是 | 300 | 刷新间隔（秒） |
| created_by | INT | - | 是 | - | 创建人ID |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 更新时间 |

### 2.3 实时数据缓存表（realtime_data_cache）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BIGINT | - | 是 | AUTO_INCREMENT | 缓存ID，主键 |
| metric_key | VARCHAR | 100 | 是 | - | 指标键值 |
| metric_value | DECIMAL | 15,4 | 是 | - | 指标值 |
| data_type | ENUM | - | 是 | - | 数据类型：counter/gauge/histogram |
| timestamp | DATETIME | - | 是 | CURRENT_TIMESTAMP | 时间戳 |
| metadata | JSON | - | 否 | - | 元数据 |
| expires_at | DATETIME | - | 是 | - | 过期时间 |

## 3. API接口设计

### 3.1 仪表盘管理

#### 3.1.1 获取仪表盘列表

**接口地址**: `GET /api/dashboard/list`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `search`: 搜索关键词
- `is_public`: 是否公开（可选）

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "dashboards": [
            {
                "id": 1,
                "name": "用户行为分析大屏",
                "description": "实时展示用户行为数据",
                "is_public": true,
                "created_by": 1,
                "created_at": "2024-01-15 10:00:00",
                "widget_count": 8
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 3,
            "total_count": 50,
            "per_page": 20
        }
    }
}
```

#### 3.1.2 获取仪表盘详情

**接口地址**: `GET /api/dashboard/{dashboard_id}`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "name": "用户行为分析大屏",
        "description": "实时展示用户行为数据",
        "layout_config": {
            "grid": {
                "rows": 12,
                "cols": 24,
                "gap": 16
            }
        },
        "widgets": [
            {
                "id": "widget_1",
                "type": "metric_card",
                "title": "在线用户数",
                "position": {"x": 0, "y": 0, "w": 6, "h": 4},
                "config": {
                    "metric": "online_users",
                    "format": "number",
                    "color": "#1890ff"
                }
            },
            {
                "id": "widget_2", 
                "type": "line_chart",
                "title": "今日访问趋势",
                "position": {"x": 6, "y": 0, "w": 12, "h": 8},
                "config": {
                    "data_source": "page_views",
                    "time_range": "today",
                    "granularity": "hour"
                }
            }
        ]
    }
}
```

#### 3.1.3 创建仪表盘

**接口地址**: `POST /api/dashboard`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "name": "string (必填, 仪表盘名称)",
    "description": "string (可选, 描述)",
    "layout_config": "object (必填, 布局配置)",
    "widgets": "array (必填, 组件配置)",
    "is_public": "boolean (可选, 是否公开)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "创建成功",
    "data": {
        "id": 1,
        "name": "用户行为分析大屏",
        "created_at": "2024-01-15 10:00:00"
    }
}
```

### 3.2 图表数据接口

#### 3.2.1 实时指标数据

**接口地址**: `GET /api/charts/realtime-metrics`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "online_users": 1250,
        "today_pv": 45678,
        "today_uv": 12345,
        "today_orders": 234,
        "today_revenue": 156789.50,
        "conversion_rate": 0.0512,
        "avg_session_duration": 285,
        "bounce_rate": 0.34,
        "timestamp": "2024-01-15 15:30:00"
    }
}
```

#### 3.2.2 趋势图表数据

**接口地址**: `GET /api/charts/trend-data`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `metric`: 指标名称 (pv/uv/orders/revenue)
- `start_date`: 开始日期
- `end_date`: 结束日期
- `granularity`: 粒度 (hour/day/week/month)

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "series": [
            {
                "name": "页面浏览量",
                "data": [
                    {"time": "2024-01-15 00:00:00", "value": 1234},
                    {"time": "2024-01-15 01:00:00", "value": 2345},
                    {"time": "2024-01-15 02:00:00", "value": 3456}
                ]
            }
        ],
        "xAxis": ["00:00", "01:00", "02:00", "03:00"],
        "summary": {
            "total": 156789,
            "average": 6532.875,
            "growth_rate": 0.12
        }
    }
}
```

#### 3.2.3 饼图数据

**接口地址**: `GET /api/charts/pie-data`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `type`: 分析类型 (traffic_source/device_type/browser/region)
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "categories": [
            {
                "name": "直接访问",
                "value": 35678,
                "percentage": 42.5,
                "color": "#1890ff"
            },
            {
                "name": "搜索引擎",
                "value": 28950,
                "percentage": 34.5,
                "color": "#52c41a"
            },
            {
                "name": "社交媒体",
                "value": 12450,
                "percentage": 14.8,
                "color": "#fa8c16"
            },
            {
                "name": "其他",
                "value": 6922,
                "percentage": 8.2,
                "color": "#eb2f96"
            }
        ],
        "total": 84000
    }
}
```

#### 3.2.4 热力图数据

**接口地址**: `GET /api/charts/heatmap-data`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `type`: 热力图类型 (page_activity/time_activity/region_activity)
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "matrix": [
            [0, 1, 2, 3, 4, 5, 6],
            [120, 150, 180, 200, 250, 300, 280],
            [150, 180, 220, 280, 350, 400, 380],
            [200, 250, 300, 380, 450, 500, 480]
        ],
        "xAxis": ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
        "yAxis": ["00-06", "06-12", "12-18", "18-24"],
        "max_value": 500,
        "min_value": 120
    }
}
```

### 3.3 地图可视化数据

#### 3.3.1 用户地域分布

**接口地址**: `GET /api/charts/geo-distribution`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `level`: 级别 (country/province/city)
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "regions": [
            {
                "name": "北京市",
                "code": "110000",
                "value": 15678,
                "percentage": 18.5,
                "coordinates": [116.4074, 39.9042]
            },
            {
                "name": "上海市",
                "code": "310000", 
                "value": 12345,
                "percentage": 14.6,
                "coordinates": [121.4737, 31.2304]
            },
            {
                "name": "广东省",
                "code": "440000",
                "value": 23456,
                "percentage": 27.8,
                "coordinates": [113.2644, 23.1291]
            }
        ],
        "total_users": 84500,
        "max_value": 23456,
        "min_value": 123
    }
}
```

#### 3.3.2 销售热力地图

**接口地址**: `GET /api/charts/sales-heatmap`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期
- `metric`: 指标 (order_count/revenue/avg_order_value)

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "heatmap_data": [
            {
                "lng": 116.4074,
                "lat": 39.9042,
                "value": 256789.50,
                "region": "北京市",
                "orders": 1234
            },
            {
                "lng": 121.4737,
                "lat": 31.2304,
                "value": 198765.30,
                "region": "上海市", 
                "orders": 987
            }
        ],
        "bounds": {
            "north": 53.5,
            "south": 18.2,
            "east": 134.8,
            "west": 73.5
        }
    }
}
```

### 3.4 自定义图表

#### 3.4.1 创建自定义图表

**接口地址**: `POST /api/charts/custom`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "chart_name": "string (必填, 图表名称)",
    "chart_type": "string (必填, 图表类型)",
    "data_source": "string (必填, 数据源)",
    "query_config": {
        "sql": "SELECT date, COUNT(*) as value FROM user_behaviors WHERE created_at >= ? GROUP BY DATE(created_at)",
        "params": ["2024-01-01"],
        "time_field": "date",
        "value_field": "value"
    },
    "style_config": {
        "colors": ["#1890ff", "#52c41a"],
        "theme": "light",
        "legend": true,
        "grid": true
    },
    "refresh_interval": 300
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "图表创建成功",
    "data": {
        "chart_id": 123,
        "chart_name": "用户行为趋势",
        "preview_url": "/api/charts/preview/123"
    }
}
```

#### 3.4.2 获取图表数据

**接口地址**: `GET /api/charts/{chart_id}/data`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `start_date`: 开始日期（可选）
- `end_date`: 结束日期（可选）
- `refresh`: 是否强制刷新（可选）

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "chart_config": {
            "type": "line",
            "title": "用户行为趋势",
            "xAxis": {
                "type": "time",
                "name": "日期"
            },
            "yAxis": {
                "type": "value",
                "name": "数量"
            }
        },
        "series_data": [
            {
                "name": "页面浏览",
                "type": "line",
                "data": [
                    ["2024-01-15", 1234],
                    ["2024-01-16", 2345],
                    ["2024-01-17", 3456]
                ]
            }
        ],
        "last_update": "2024-01-17 15:30:00"
    }
}
```

## 4. 前端组件设计

### 4.1 图表组件库

#### 4.1.1 基础图表组件

```vue
<!-- LineChart.vue -->
<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'LineChart',
  props: {
    data: {
      type: Object,
      required: true
    },
    options: {
      type: Object,
      default: () => ({})
    },
    height: {
      type: String,
      default: '400px'
    }
  },
  mounted() {
    this.initChart()
  },
  watch: {
    data: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
    },
    updateChart() {
      const option = {
        title: {
          text: this.data.title
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: this.data.xAxis
        },
        yAxis: {
          type: 'value'
        },
        series: this.data.series,
        ...this.options
      }
      this.chart.setOption(option)
    }
  }
}
</script>
```

#### 4.1.2 实时数据卡片组件

```vue
<!-- MetricCard.vue -->
<template>
  <div class="metric-card">
    <div class="metric-title">{{ title }}</div>
    <div class="metric-value">
      <span class="value">{{ formattedValue }}</span>
      <span class="unit" v-if="unit">{{ unit }}</span>
    </div>
    <div class="metric-trend" v-if="trend">
      <span :class="['trend-value', trendClass]">
        <i :class="trendIcon"></i>
        {{ trend.value }}%
      </span>
      <span class="trend-label">{{ trend.label }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MetricCard',
  props: {
    title: String,
    value: [Number, String],
    unit: String,
    format: {
      type: String,
      default: 'number'
    },
    trend: Object
  },
  computed: {
    formattedValue() {
      if (this.format === 'currency') {
        return new Intl.NumberFormat('zh-CN', {
          style: 'currency',
          currency: 'CNY'
        }).format(this.value)
      } else if (this.format === 'percentage') {
        return (this.value * 100).toFixed(2) + '%'
      } else {
        return new Intl.NumberFormat('zh-CN').format(this.value)
      }
    },
    trendClass() {
      return this.trend?.direction === 'up' ? 'trend-up' : 'trend-down'
    },
    trendIcon() {
      return this.trend?.direction === 'up' ? 'icon-arrow-up' : 'icon-arrow-down'
    }
  }
}
</script>
```

### 4.2 仪表盘组件

#### 4.2.1 可拖拽网格布局

```vue
<!-- DashboardGrid.vue -->
<template>
  <grid-layout
    :layout="layout"
    :col-num="24"
    :row-height="30"
    :is-draggable="editable"
    :is-resizable="editable"
    :margin="[16, 16]"
    @layout-updated="updateLayout"
  >
    <grid-item
      v-for="widget in widgets"
      :key="widget.id"
      :x="widget.x"
      :y="widget.y"
      :w="widget.w"
      :h="widget.h"
      :i="widget.id"
    >
      <widget-container
        :widget="widget"
        :editable="editable"
        @update="updateWidget"
        @delete="deleteWidget"
      />
    </grid-item>
  </grid-layout>
</template>

<script>
import { GridLayout, GridItem } from 'vue-grid-layout'
import WidgetContainer from './WidgetContainer.vue'

export default {
  name: 'DashboardGrid',
  components: {
    GridLayout,
    GridItem,
    WidgetContainer
  },
  props: {
    widgets: Array,
    editable: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    layout() {
      return this.widgets.map(widget => ({
        i: widget.id,
        x: widget.x,
        y: widget.y,
        w: widget.w,
        h: widget.h
      }))
    }
  },
  methods: {
    updateLayout(newLayout) {
      this.$emit('layout-change', newLayout)
    },
    updateWidget(widget) {
      this.$emit('widget-update', widget)
    },
    deleteWidget(widgetId) {
      this.$emit('widget-delete', widgetId)
    }
  }
}
</script>
```

### 4.3 地图可视化组件

#### 4.3.1 地理分布地图

```vue
<!-- GeoMap.vue -->
<template>
  <div class="geo-map" ref="mapContainer"></div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts/map/js/china'

export default {
  name: 'GeoMap',
  props: {
    data: Array,
    mapType: {
      type: String,
      default: 'china'
    }
  },
  mounted() {
    this.initMap()
  },
  watch: {
    data() {
      this.updateMap()
    }
  },
  methods: {
    initMap() {
      this.chart = echarts.init(this.$refs.mapContainer)
      this.updateMap()
    },
    updateMap() {
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}'
        },
        visualMap: {
          min: 0,
          max: Math.max(...this.data.map(item => item.value)),
          left: 'left',
          top: 'bottom',
          text: ['高', '低'],
          calculable: true,
          inRange: {
            color: ['#e0f3ff', '#006edd']
          }
        },
        series: [{
          name: '用户分布',
          type: 'map',
          map: this.mapType,
          roam: false,
          data: this.data
        }]
      }
      this.chart.setOption(option)
    }
  }
}
</script>
```

## 5. 实时数据更新

### 5.1 WebSocket连接

```javascript
// websocket.js
class WebSocketClient {
  constructor(url, options = {}) {
    this.url = url
    this.options = options
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 5000
    this.subscribers = new Map()
  }

  connect() {
    this.ws = new WebSocket(this.url)
    
    this.ws.onopen = () => {
      console.log('WebSocket连接已建立')
      this.reconnectAttempts = 0
    }
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.handleMessage(data)
    }
    
    this.ws.onclose = () => {
      console.log('WebSocket连接已关闭')
      this.reconnect()
    }
    
    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error)
    }
  }

  handleMessage(data) {
    const { type, payload } = data
    if (this.subscribers.has(type)) {
      this.subscribers.get(type).forEach(callback => {
        callback(payload)
      })
    }
  }

  subscribe(type, callback) {
    if (!this.subscribers.has(type)) {
      this.subscribers.set(type, [])
    }
    this.subscribers.get(type).push(callback)
  }

  unsubscribe(type, callback) {
    if (this.subscribers.has(type)) {
      const callbacks = this.subscribers.get(type)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      setTimeout(() => {
        console.log(`重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
        this.connect()
      }, this.reconnectInterval)
    }
  }
}

export default WebSocketClient
```

### 5.2 Vue实时数据混入

```javascript
// realtime-mixin.js
import WebSocketClient from './websocket'

export default {
  data() {
    return {
      wsClient: null,
      realtimeData: {}
    }
  },
  created() {
    this.initWebSocket()
  },
  beforeDestroy() {
    if (this.wsClient) {
      this.wsClient.disconnect()
    }
  },
  methods: {
    initWebSocket() {
      this.wsClient = new WebSocketClient('/ws/realtime/')
      this.wsClient.connect()
      
      // 订阅实时指标更新
      this.wsClient.subscribe('metrics_update', (data) => {
        this.realtimeData = { ...this.realtimeData, ...data }
        this.onRealtimeUpdate(data)
      })
      
      // 订阅图表数据更新
      this.wsClient.subscribe('chart_update', (data) => {
        this.onChartUpdate(data)
      })
    },
    onRealtimeUpdate(data) {
      // 子组件重写此方法
    },
    onChartUpdate(data) {
      // 子组件重写此方法
    }
  }
}
```

## 6. 主题和样式配置

### 6.1 图表主题配置

```javascript
// chart-themes.js
export const lightTheme = {
  color: [
    '#1890ff', '#52c41a', '#fa8c16', '#eb2f96', 
    '#722ed1', '#13c2c2', '#fadb14', '#a0d911'
  ],
  backgroundColor: '#ffffff',
  textStyle: {
    color: '#333333'
  },
  title: {
    textStyle: {
      color: '#333333'
    }
  },
  legend: {
    textStyle: {
      color: '#333333'
    }
  },
  grid: {
    borderColor: '#f0f0f0'
  }
}

export const darkTheme = {
  color: [
    '#4dabf7', '#69db7c', '#ffd43b', '#f783ac',
    '#9775fa', '#3bc9db', '#ffd43b', '#8ce99a'
  ],
  backgroundColor: '#1f1f1f',
  textStyle: {
    color: '#ffffff'
  },
  title: {
    textStyle: {
      color: '#ffffff'
    }
  },
  legend: {
    textStyle: {
      color: '#ffffff'
    }
  },
  grid: {
    borderColor: '#484848'
  }
}
```

### 6.2 响应式布局

```scss
// dashboard.scss
.dashboard-container {
  padding: 16px;
  background: #f5f5f5;
  min-height: 100vh;

  .dashboard-header {
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .dashboard-title {
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }

    .dashboard-actions {
      display: flex;
      gap: 12px;
    }
  }

  .widget-container {
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;

    .widget-header {
      padding: 16px 20px 12px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .widget-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .widget-actions {
        display: flex;
        gap: 8px;
      }
    }

    .widget-content {
      padding: 20px;
    }
  }

  .metric-card {
    text-align: center;
    padding: 24px 16px;

    .metric-title {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .metric-value {
      font-size: 32px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;

      .unit {
        font-size: 16px;
        color: #999;
        margin-left: 4px;
      }
    }

    .metric-trend {
      font-size: 12px;

      .trend-value {
        &.trend-up {
          color: #52c41a;
        }
        &.trend-down {
          color: #f5222d;
        }
      }

      .trend-label {
        color: #999;
        margin-left: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-container {
    padding: 8px;

    .dashboard-header {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;
    }

    .widget-container {
      .widget-content {
        padding: 16px;
      }
    }

    .metric-card {
      padding: 16px 8px;

      .metric-value {
        font-size: 24px;
      }
    }
  }
}
```

## 7. 性能优化

### 7.1 图表渲染优化

```javascript
// chart-performance.js
export const optimizeChartOptions = {
  // 大数据量优化
  animation: false,
  silent: true,
  
  // 数据采样
  sampling: 'average',
  
  // 渐进式渲染
  progressive: 1000,
  progressiveThreshold: 3000,
  
  // 懒加载
  lazyUpdate: true
}

// 数据分页加载
export class ChartDataLoader {
  constructor(apiUrl, pageSize = 1000) {
    this.apiUrl = apiUrl
    this.pageSize = pageSize
    this.currentPage = 1
    this.totalPages = 1
    this.loading = false
  }

  async loadNextBatch() {
    if (this.loading || this.currentPage > this.totalPages) {
      return null
    }

    this.loading = true
    try {
      const response = await fetch(`${this.apiUrl}?page=${this.currentPage}&limit=${this.pageSize}`)
      const data = await response.json()
      
      this.currentPage++
      this.totalPages = data.pagination.total_pages
      
      return data.data
    } finally {
      this.loading = false
    }
  }
}
```

### 7.2 内存管理

```javascript
// memory-management.js
export class ChartManager {
  constructor() {
    this.charts = new Map()
    this.observers = new Map()
  }

  registerChart(id, chart) {
    this.charts.set(id, chart)
    
    // 使用Intersection Observer检测图表可见性
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.resumeChart(id)
        } else {
          this.pauseChart(id)
        }
      })
    })
    
    observer.observe(chart.getDom())
    this.observers.set(id, observer)
  }

  pauseChart(id) {
    const chart = this.charts.get(id)
    if (chart) {
      chart.dispose()
    }
  }

  resumeChart(id) {
    const chart = this.charts.get(id)
    if (chart && chart.isDisposed()) {
      // 重新初始化图表
      this.reinitializeChart(id)
    }
  }

  destroyChart(id) {
    const chart = this.charts.get(id)
    const observer = this.observers.get(id)
    
    if (chart) {
      chart.dispose()
      this.charts.delete(id)
    }
    
    if (observer) {
      observer.disconnect()
      this.observers.delete(id)
    }
  }
}
```
