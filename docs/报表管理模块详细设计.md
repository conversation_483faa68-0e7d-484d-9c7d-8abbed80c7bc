# 报表管理模块详细设计文档

## 1. 模块概述

报表管理模块负责生成、管理和分发各类数据分析报表，支持定时生成报表、报表模板管理、报表导出和邮件推送等功能。为业务决策提供定期的数据汇总和分析报告。

## 2. 数据库设计

### 2.1 报表模板表（report_templates）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | 是 | AUTO_INCREMENT | 模板ID，主键 |
| template_name | VARCHAR | 100 | 是 | - | 模板名称 |
| template_type | ENUM | - | 是 | - | 模板类型：daily/weekly/monthly/custom |
| description | TEXT | - | 否 | - | 模板描述 |
| template_config | JSON | - | 是 | - | 模板配置 |
| data_sources | JSON | - | 是 | - | 数据源配置 |
| layout_config | JSON | - | 是 | - | 布局配置 |
| status | ENUM | - | 是 | 'active' | 状态：active/inactive |
| created_by | INT | - | 是 | - | 创建人ID |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 更新时间 |

### 2.2 报表实例表（report_instances）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BIGINT | - | 是 | AUTO_INCREMENT | 实例ID，主键 |
| template_id | INT | - | 是 | - | 模板ID，外键 |
| report_name | VARCHAR | 100 | 是 | - | 报表名称 |
| report_period | VARCHAR | 50 | 是 | - | 报表周期 |
| start_date | DATE | - | 是 | - | 统计开始日期 |
| end_date | DATE | - | 是 | - | 统计结束日期 |
| report_data | JSON | - | 否 | - | 报表数据 |
| file_path | VARCHAR | 500 | 否 | - | 文件路径 |
| file_size | BIGINT | - | 否 | - | 文件大小 |
| generation_status | ENUM | - | 是 | 'pending' | 生成状态：pending/generating/completed/failed |
| error_message | TEXT | - | 否 | - | 错误信息 |
| generated_by | INT | - | 是 | - | 生成人ID |
| generated_at | DATETIME | - | 否 | - | 生成时间 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

### 2.3 报表订阅表（report_subscriptions）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | 是 | AUTO_INCREMENT | 订阅ID，主键 |
| template_id | INT | - | 是 | - | 模板ID，外键 |
| user_id | INT | - | 是 | - | 用户ID，外键 |
| email | VARCHAR | 100 | 是 | - | 接收邮箱 |
| schedule_type | ENUM | - | 是 | - | 调度类型：daily/weekly/monthly |
| schedule_config | JSON | - | 是 | - | 调度配置 |
| is_active | BOOLEAN | - | 是 | TRUE | 是否激活 |
| last_sent_at | DATETIME | - | 否 | - | 最后发送时间 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 更新时间 |

### 2.4 报表发送记录表（report_send_logs）

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BIGINT | - | 是 | AUTO_INCREMENT | 记录ID，主键 |
| subscription_id | INT | - | 是 | - | 订阅ID，外键 |
| report_instance_id | BIGINT | - | 是 | - | 报表实例ID，外键 |
| recipient_email | VARCHAR | 100 | 是 | - | 接收邮箱 |
| send_status | ENUM | - | 是 | - | 发送状态：pending/sent/failed |
| error_message | TEXT | - | 否 | - | 错误信息 |
| sent_at | DATETIME | - | 否 | - | 发送时间 |
| created_at | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

## 3. API接口设计

### 3.1 报表模板管理

#### 3.1.1 获取报表模板列表

**接口地址**: `GET /api/reports/templates`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `template_type`: 模板类型筛选
- `status`: 状态筛选
- `search`: 搜索关键词

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "templates": [
            {
                "id": 1,
                "template_name": "日报模板",
                "template_type": "daily",
                "description": "每日用户行为分析报表",
                "status": "active",
                "created_by": 1,
                "created_at": "2024-01-15 10:00:00",
                "report_count": 30
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 3,
            "total_count": 50,
            "per_page": 20
        }
    }
}
```

#### 3.1.2 获取报表模板详情

**接口地址**: `GET /api/reports/templates/{template_id}`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "template_name": "日报模板",
        "template_type": "daily",
        "description": "每日用户行为分析报表",
        "template_config": {
            "title": "用户行为日报",
            "subtitle": "{{date}} 数据分析报告",
            "sections": [
                {
                    "title": "核心指标",
                    "type": "metrics",
                    "metrics": [
                        {
                            "name": "总访问量",
                            "field": "total_pv",
                            "format": "number"
                        },
                        {
                            "name": "独立访客",
                            "field": "total_uv", 
                            "format": "number"
                        }
                    ]
                },
                {
                    "title": "趋势分析",
                    "type": "chart",
                    "chart_type": "line",
                    "data_source": "daily_trend"
                }
            ]
        },
        "data_sources": [
            {
                "name": "daily_metrics",
                "query": "SELECT COUNT(*) as total_pv, COUNT(DISTINCT user_id) as total_uv FROM page_views WHERE DATE(created_at) = ?",
                "params": ["{{date}}"]
            }
        ],
        "layout_config": {
            "page_size": "A4",
            "orientation": "portrait",
            "margins": {
                "top": 20,
                "right": 20,
                "bottom": 20,
                "left": 20
            }
        }
    }
}
```

#### 3.1.3 创建报表模板

**接口地址**: `POST /api/reports/templates`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "template_name": "string (必填, 模板名称)",
    "template_type": "string (必填, 模板类型)",
    "description": "string (可选, 描述)",
    "template_config": "object (必填, 模板配置)",
    "data_sources": "array (必填, 数据源配置)",
    "layout_config": "object (必填, 布局配置)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "创建成功",
    "data": {
        "id": 1,
        "template_name": "日报模板",
        "template_type": "daily",
        "created_at": "2024-01-15 10:00:00"
    }
}
```

### 3.2 报表生成

#### 3.2.1 手动生成报表

**接口地址**: `POST /api/reports/generate`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "template_id": "int (必填, 模板ID)",
    "start_date": "string (必填, 开始日期)",
    "end_date": "string (必填, 结束日期)",
    "report_name": "string (可选, 报表名称)",
    "export_format": "string (可选, 导出格式: pdf/excel/html)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "报表生成任务已创建",
    "data": {
        "report_instance_id": 12345,
        "status": "pending",
        "estimated_time": 120
    }
}
```

#### 3.2.2 获取报表生成状态

**接口地址**: `GET /api/reports/instances/{instance_id}/status`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "instance_id": 12345,
        "status": "completed",
        "progress": 100,
        "file_url": "/api/reports/instances/12345/download",
        "file_size": 2048576,
        "generated_at": "2024-01-15 10:30:00"
    }
}
```

#### 3.2.3 预览报表

**接口地址**: `POST /api/reports/preview`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "template_id": "int (必填, 模板ID)",
    "start_date": "string (必填, 开始日期)",
    "end_date": "string (必填, 结束日期)",
    "preview_type": "string (可选, 预览类型: html/thumbnail)"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "预览生成成功",
    "data": {
        "preview_url": "/api/reports/preview/abc123.html",
        "thumbnail_url": "/api/reports/preview/abc123.png",
        "expires_at": "2024-01-15 12:00:00"
    }
}
```

### 3.3 报表查询

#### 3.3.1 获取报表实例列表

**接口地址**: `GET /api/reports/instances`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `template_id`: 模板ID筛选
- `status`: 状态筛选
- `start_date`: 开始日期筛选
- `end_date`: 结束日期筛选

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "reports": [
            {
                "id": 12345,
                "template_id": 1,
                "template_name": "日报模板",
                "report_name": "2024-01-15 用户行为日报",
                "report_period": "2024-01-15",
                "start_date": "2024-01-15",
                "end_date": "2024-01-15",
                "generation_status": "completed",
                "file_size": 2048576,
                "generated_at": "2024-01-15 10:30:00",
                "download_url": "/api/reports/instances/12345/download"
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 10,
            "total_count": 200,
            "per_page": 20
        }
    }
}
```

#### 3.3.2 下载报表

**接口地址**: `GET /api/reports/instances/{instance_id}/download`

**请求头**: `Authorization: Bearer {token}`

**响应**: 文件流下载

#### 3.3.3 获取报表数据

**接口地址**: `GET /api/reports/instances/{instance_id}/data`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "report_info": {
            "id": 12345,
            "report_name": "2024-01-15 用户行为日报",
            "period": "2024-01-15",
            "generated_at": "2024-01-15 10:30:00"
        },
        "sections": [
            {
                "title": "核心指标",
                "type": "metrics",
                "data": {
                    "total_pv": 156789,
                    "total_uv": 23456,
                    "conversion_rate": 0.0345,
                    "bounce_rate": 0.3421
                }
            },
            {
                "title": "趋势分析",
                "type": "chart",
                "chart_type": "line",
                "data": {
                    "xAxis": ["00:00", "01:00", "02:00"],
                    "series": [
                        {
                            "name": "PV",
                            "data": [1234, 2345, 3456]
                        }
                    ]
                }
            }
        ]
    }
}
```

### 3.4 报表订阅

#### 3.4.1 创建报表订阅

**接口地址**: `POST /api/reports/subscriptions`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "template_id": "int (必填, 模板ID)",
    "email": "string (必填, 接收邮箱)",
    "schedule_type": "string (必填, 调度类型)",
    "schedule_config": {
        "time": "09:00",
        "day_of_week": 1,
        "day_of_month": 1
    }
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "订阅创建成功",
    "data": {
        "subscription_id": 789,
        "template_name": "日报模板",
        "schedule_description": "每日上午9点发送"
    }
}
```

#### 3.4.2 获取我的订阅列表

**接口地址**: `GET /api/reports/subscriptions/my`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "subscriptions": [
            {
                "id": 789,
                "template_id": 1,
                "template_name": "日报模板",
                "email": "<EMAIL>",
                "schedule_type": "daily",
                "schedule_config": {
                    "time": "09:00"
                },
                "is_active": true,
                "last_sent_at": "2024-01-15 09:00:00",
                "created_at": "2024-01-01 10:00:00"
            }
        ]
    }
}
```

#### 3.4.3 取消报表订阅

**接口地址**: `DELETE /api/reports/subscriptions/{subscription_id}`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "订阅已取消"
}
```

### 3.5 报表发送记录

#### 3.5.1 获取发送记录

**接口地址**: `GET /api/reports/send-logs`

**请求头**: `Authorization: Bearer {admin_token}`

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `template_id`: 模板ID筛选
- `status`: 发送状态筛选
- `start_date`: 开始日期筛选
- `end_date`: 结束日期筛选

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "logs": [
            {
                "id": 1001,
                "template_name": "日报模板",
                "report_name": "2024-01-15 用户行为日报",
                "recipient_email": "<EMAIL>",
                "send_status": "sent",
                "sent_at": "2024-01-15 09:00:00"
            }
        ],
        "summary": {
            "total_sent": 150,
            "success_rate": 98.5,
            "failed_count": 3
        },
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_count": 100,
            "per_page": 20
        }
    }
}
```

## 4. 报表模板引擎

### 4.1 模板语法

```html
<!-- 报表HTML模板示例 -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{report_title}}</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; }
        .header { text-align: center; margin-bottom: 30px; }
        .metric-card { display: inline-block; margin: 10px; padding: 20px; border: 1px solid #ddd; }
        .chart-container { margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{report_title}}</h1>
        <p>报表周期：{{start_date}} 至 {{end_date}}</p>
        <p>生成时间：{{generated_at}}</p>
    </div>

    <!-- 核心指标区域 -->
    <div class="metrics-section">
        <h2>核心指标</h2>
        {{#each metrics}}
        <div class="metric-card">
            <h3>{{this.name}}</h3>
            <div class="metric-value">{{format this.value this.format}}</div>
            {{#if this.trend}}
            <div class="metric-trend {{this.trend.direction}}">
                {{this.trend.value}}%
            </div>
            {{/if}}
        </div>
        {{/each}}
    </div>

    <!-- 图表区域 -->
    {{#each charts}}
    <div class="chart-section">
        <h2>{{this.title}}</h2>
        <div class="chart-container">
            {{render_chart this}}
        </div>
    </div>
    {{/each}}

    <!-- 表格区域 -->
    {{#each tables}}
    <div class="table-section">
        <h2>{{this.title}}</h2>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <thead>
                <tr>
                    {{#each this.columns}}
                    <th>{{this}}</th>
                    {{/each}}
                </tr>
            </thead>
            <tbody>
                {{#each this.rows}}
                <tr>
                    {{#each this}}
                    <td>{{this}}</td>
                    {{/each}}
                </tr>
                {{/each}}
            </tbody>
        </table>
    </div>
    {{/each}}
</body>
</html>
```

### 4.2 Python模板处理器

```python
# report_generator.py
import json
from jinja2 import Template
from datetime import datetime, timedelta
import pandas as pd
from sqlalchemy import text

class ReportGenerator:
    def __init__(self, db_connection):
        self.db = db_connection
        
    def generate_report(self, template_config, start_date, end_date):
        """生成报表"""
        # 1. 获取数据
        report_data = self._fetch_report_data(
            template_config['data_sources'], 
            start_date, 
            end_date
        )
        
        # 2. 处理模板
        template_content = template_config['template_config']
        processed_data = self._process_template_data(template_content, report_data)
        
        # 3. 渲染HTML
        html_content = self._render_template(template_content, processed_data)
        
        return {
            'html_content': html_content,
            'data': processed_data,
            'generated_at': datetime.now()
        }
    
    def _fetch_report_data(self, data_sources, start_date, end_date):
        """获取报表数据"""
        data = {}
        
        for source in data_sources:
            query = source['query']
            params = []
            
            # 处理参数替换
            for param in source.get('params', []):
                if param == '{{start_date}}':
                    params.append(start_date)
                elif param == '{{end_date}}':
                    params.append(end_date)
                else:
                    params.append(param)
            
            # 执行查询
            result = self.db.execute(text(query), params)
            data[source['name']] = result.fetchall()
        
        return data
    
    def _process_template_data(self, template_config, raw_data):
        """处理模板数据"""
        processed = {
            'report_title': template_config.get('title', ''),
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'sections': []
        }
        
        # 处理各个部分
        for section in template_config.get('sections', []):
            if section['type'] == 'metrics':
                processed['sections'].append(
                    self._process_metrics_section(section, raw_data)
                )
            elif section['type'] == 'chart':
                processed['sections'].append(
                    self._process_chart_section(section, raw_data)
                )
            elif section['type'] == 'table':
                processed['sections'].append(
                    self._process_table_section(section, raw_data)
                )
        
        return processed
    
    def _process_metrics_section(self, section_config, raw_data):
        """处理指标部分"""
        metrics = []
        source_data = raw_data.get(section_config['data_source'], [])
        
        if source_data:
            row = source_data[0]  # 假设指标数据只有一行
            
            for metric in section_config['metrics']:
                value = getattr(row, metric['field'], 0)
                formatted_value = self._format_value(value, metric.get('format', 'number'))
                
                metrics.append({
                    'name': metric['name'],
                    'value': value,
                    'formatted_value': formatted_value,
                    'trend': self._calculate_trend(metric, raw_data) if 'trend_source' in metric else None
                })
        
        return {
            'type': 'metrics',
            'title': section_config['title'],
            'metrics': metrics
        }
    
    def _process_chart_section(self, section_config, raw_data):
        """处理图表部分"""
        source_data = raw_data.get(section_config['data_source'], [])
        
        chart_data = {
            'type': 'chart',
            'title': section_config['title'],
            'chart_type': section_config['chart_type'],
            'data': {
                'xAxis': [],
                'series': []
            }
        }
        
        if source_data:
            # 转换为图表数据格式
            df = pd.DataFrame(source_data)
            
            chart_data['data']['xAxis'] = df[section_config['x_field']].tolist()
            
            for series in section_config['series']:
                chart_data['data']['series'].append({
                    'name': series['name'],
                    'data': df[series['field']].tolist()
                })
        
        return chart_data
    
    def _format_value(self, value, format_type):
        """格式化数值"""
        if format_type == 'currency':
            return f'¥{value:,.2f}'
        elif format_type == 'percentage':
            return f'{value * 100:.2f}%'
        elif format_type == 'number':
            return f'{value:,}'
        else:
            return str(value)
```

### 4.3 PDF生成器

```python
# pdf_generator.py
from weasyprint import HTML, CSS
from io import BytesIO
import base64

class PDFGenerator:
    def __init__(self):
        self.default_css = """
            @page {
                size: A4;
                margin: 2cm;
                @bottom-center {
                    content: "第 " counter(page) " 页 / 共 " counter(pages) " 页";
                    font-size: 10px;
                    color: #666;
                }
            }
            
            body {
                font-family: 'Microsoft YaHei', sans-serif;
                font-size: 12px;
                line-height: 1.6;
                color: #333;
            }
            
            h1, h2, h3 {
                color: #2c3e50;
                margin-bottom: 1em;
            }
            
            .metric-card {
                display: inline-block;
                width: 200px;
                margin: 10px;
                padding: 20px;
                border: 1px solid #e1e8ed;
                border-radius: 6px;
                text-align: center;
                background: #f8f9fa;
            }
            
            .metric-value {
                font-size: 24px;
                font-weight: bold;
                color: #1890ff;
            }
            
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }
            
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            
            .chart-container {
                margin: 20px 0;
                text-align: center;
            }
            
            .chart-image {
                max-width: 100%;
                height: auto;
            }
        """
    
    def generate_pdf(self, html_content, charts_images=None):
        """生成PDF"""
        # 处理图表图片
        if charts_images:
            html_content = self._embed_chart_images(html_content, charts_images)
        
        # 生成PDF
        html_doc = HTML(string=html_content)
        css_doc = CSS(string=self.default_css)
        
        pdf_buffer = BytesIO()
        html_doc.write_pdf(pdf_buffer, stylesheets=[css_doc])
        
        return pdf_buffer.getvalue()
    
    def _embed_chart_images(self, html_content, charts_images):
        """嵌入图表图片"""
        for chart_id, image_data in charts_images.items():
            # 将图片转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            img_tag = f'<img src="data:image/png;base64,{image_base64}" class="chart-image" />'
            
            # 替换图表占位符
            placeholder = f'{{{{chart:{chart_id}}}}}'
            html_content = html_content.replace(placeholder, img_tag)
        
        return html_content
```

## 5. 定时任务调度

### 5.1 Celery任务定义

```python
# tasks.py
from celery import Celery
from celery.schedules import crontab
from datetime import datetime, timedelta
from .report_generator import ReportGenerator
from .email_sender import EmailSender

app = Celery('report_tasks')

@app.task
def generate_scheduled_report(template_id, report_date=None):
    """生成定时报表"""
    if not report_date:
        report_date = datetime.now().date() - timedelta(days=1)
    
    try:
        # 获取模板配置
        template = ReportTemplate.objects.get(id=template_id)
        
        # 生成报表
        generator = ReportGenerator()
        report_data = generator.generate_report(
            template.template_config,
            report_date,
            report_date
        )
        
        # 保存报表实例
        instance = ReportInstance.objects.create(
            template_id=template_id,
            report_name=f"{template.template_name}_{report_date}",
            report_period=str(report_date),
            start_date=report_date,
            end_date=report_date,
            report_data=report_data['data'],
            generation_status='completed',
            generated_at=datetime.now()
        )
        
        # 发送邮件给订阅者
        send_report_to_subscribers.delay(instance.id)
        
        return {"status": "success", "instance_id": instance.id}
        
    except Exception as e:
        return {"status": "failed", "error": str(e)}

@app.task
def send_report_to_subscribers(instance_id):
    """发送报表给订阅者"""
    instance = ReportInstance.objects.get(id=instance_id)
    subscriptions = ReportSubscription.objects.filter(
        template_id=instance.template_id,
        is_active=True
    )
    
    email_sender = EmailSender()
    
    for subscription in subscriptions:
        try:
            # 发送邮件
            email_sender.send_report_email(
                instance,
                subscription.email
            )
            
            # 记录发送日志
            ReportSendLog.objects.create(
                subscription_id=subscription.id,
                report_instance_id=instance.id,
                recipient_email=subscription.email,
                send_status='sent',
                sent_at=datetime.now()
            )
            
        except Exception as e:
            # 记录失败日志
            ReportSendLog.objects.create(
                subscription_id=subscription.id,
                report_instance_id=instance.id,
                recipient_email=subscription.email,
                send_status='failed',
                error_message=str(e)
            )

# 定时任务配置
app.conf.beat_schedule = {
    'daily-reports': {
        'task': 'generate_daily_reports',
        'schedule': crontab(hour=6, minute=0),  # 每天早上6点生成日报
    },
    'weekly-reports': {
        'task': 'generate_weekly_reports', 
        'schedule': crontab(hour=7, minute=0, day_of_week=1),  # 每周一早上7点生成周报
    },
    'monthly-reports': {
        'task': 'generate_monthly_reports',
        'schedule': crontab(hour=8, minute=0, day=1),  # 每月1号早上8点生成月报
    },
}
```

### 5.2 邮件发送服务

```python
# email_sender.py
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
import os

class EmailSender:
    def __init__(self):
        self.smtp_server = os.getenv('SMTP_SERVER', 'smtp.example.com')
        self.smtp_port = int(os.getenv('SMTP_PORT', 587))
        self.smtp_username = os.getenv('SMTP_USERNAME')
        self.smtp_password = os.getenv('SMTP_PASSWORD')
        self.from_email = os.getenv('FROM_EMAIL')
    
    def send_report_email(self, report_instance, recipient_email):
        """发送报表邮件"""
        # 创建邮件
        msg = MIMEMultipart()
        msg['From'] = self.from_email
        msg['To'] = recipient_email
        msg['Subject'] = f"数据分析报表 - {report_instance.report_name}"
        
        # 邮件正文
        body = f"""
        尊敬的用户，
        
        您订阅的数据分析报表已生成完成，请查看附件。
        
        报表信息：
        - 报表名称：{report_instance.report_name}
        - 统计周期：{report_instance.start_date} 至 {report_instance.end_date}
        - 生成时间：{report_instance.generated_at.strftime('%Y-%m-%d %H:%M:%S')}
        
        如有疑问，请联系系统管理员。
        
        此邮件由系统自动发送，请勿回复。
        """
        
        msg.attach(MIMEText(body, 'plain', 'utf-8'))
        
        # 添加PDF附件
        if report_instance.file_path and os.path.exists(report_instance.file_path):
            with open(report_instance.file_path, 'rb') as f:
                attachment = MIMEApplication(f.read(), _subtype='pdf')
                attachment.add_header(
                    'Content-Disposition', 
                    'attachment', 
                    filename=f"{report_instance.report_name}.pdf"
                )
                msg.attach(attachment)
        
        # 发送邮件
        try:
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(msg)
            
            return True
            
        except Exception as e:
            print(f"邮件发送失败: {e}")
            return False
```

## 6. 前端报表管理界面

### 6.1 报表模板管理页面

```vue
<!-- ReportTemplates.vue -->
<template>
  <div class="report-templates">
    <div class="page-header">
      <h2>报表模板管理</h2>
      <el-button type="primary" @click="showCreateDialog">
        新建模板
      </el-button>
    </div>

    <div class="filters">
      <el-form inline>
        <el-form-item label="模板类型">
          <el-select v-model="filters.template_type">
            <el-option label="全部" value=""></el-option>
            <el-option label="日报" value="daily"></el-option>
            <el-option label="周报" value="weekly"></el-option>
            <el-option label="月报" value="monthly"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status">
            <el-option label="全部" value=""></el-option>
            <el-option label="激活" value="active"></el-option>
            <el-option label="停用" value="inactive"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="loadTemplates">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="templates" stripe>
      <el-table-column prop="template_name" label="模板名称" width="200" />
      <el-table-column prop="template_type" label="类型" width="100">
        <template #default="{ row }">
          <el-tag :type="getTypeColor(row.template_type)">
            {{ getTypeName(row.template_type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" />
      <el-table-column prop="report_count" label="报表数量" width="100" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
            {{ row.status === 'active' ? '激活' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="editTemplate(row)">编辑</el-button>
          <el-button size="small" @click="previewTemplate(row)">预览</el-button>
          <el-button 
            size="small" 
            type="primary" 
            @click="generateReport(row)"
          >
            生成
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="pagination.current_page"
      :page-size="pagination.per_page"
      :total="pagination.total_count"
      @current-change="loadTemplates"
    />
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getReportTemplates, generateReport } from '@/api/reports'

export default {
  name: 'ReportTemplates',
  setup() {
    const templates = ref([])
    const filters = reactive({
      template_type: '',
      status: ''
    })
    const pagination = reactive({
      current_page: 1,
      per_page: 20,
      total_count: 0
    })

    const loadTemplates = async () => {
      try {
        const response = await getReportTemplates({
          page: pagination.current_page,
          limit: pagination.per_page,
          ...filters
        })
        
        templates.value = response.data.templates
        Object.assign(pagination, response.data.pagination)
      } catch (error) {
        ElMessage.error('加载模板失败')
      }
    }

    const generateReport = async (template) => {
      try {
        const response = await generateReport({
          template_id: template.id,
          start_date: '2024-01-15',
          end_date: '2024-01-15'
        })
        
        ElMessage.success('报表生成任务已创建')
        // 跳转到报表列表页面查看生成状态
        this.$router.push('/reports/instances')
      } catch (error) {
        ElMessage.error('生成报表失败')
      }
    }

    const getTypeName = (type) => {
      const typeMap = {
        daily: '日报',
        weekly: '周报', 
        monthly: '月报',
        custom: '自定义'
      }
      return typeMap[type] || type
    }

    const getTypeColor = (type) => {
      const colorMap = {
        daily: 'primary',
        weekly: 'success',
        monthly: 'warning',
        custom: 'info'
      }
      return colorMap[type] || 'info'
    }

    onMounted(() => {
      loadTemplates()
    })

    return {
      templates,
      filters,
      pagination,
      loadTemplates,
      generateReport,
      getTypeName,
      getTypeColor
    }
  }
}
</script>
```

## 7. 性能优化

### 7.1 异步报表生成

```python
# 使用Celery进行异步处理
@app.task(bind=True)
def generate_large_report(self, template_id, start_date, end_date):
    """生成大型报表的异步任务"""
    try:
        # 更新任务状态
        self.update_state(
            state='PROCESSING',
            meta={'current': 0, 'total': 100, 'status': '开始生成报表...'}
        )
        
        # 分步处理数据
        generator = ReportGenerator()
        
        # 步骤1: 获取数据 (30%)
        self.update_state(
            state='PROCESSING',
            meta={'current': 30, 'total': 100, 'status': '获取数据中...'}
        )
        data = generator.fetch_data(template_id, start_date, end_date)
        
        # 步骤2: 处理数据 (60%)
        self.update_state(
            state='PROCESSING', 
            meta={'current': 60, 'total': 100, 'status': '处理数据中...'}
        )
        processed_data = generator.process_data(data)
        
        # 步骤3: 生成文件 (90%)
        self.update_state(
            state='PROCESSING',
            meta={'current': 90, 'total': 100, 'status': '生成文件中...'}
        )
        file_path = generator.generate_file(processed_data)
        
        # 完成
        return {
            'status': 'completed',
            'file_path': file_path,
            'message': '报表生成完成'
        }
        
    except Exception as e:
        self.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        raise
```

### 7.2 缓存策略

```python
# 报表数据缓存
import redis
from django.core.cache import cache

class ReportCache:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=1)
        self.cache_timeout = 3600  # 1小时
    
    def get_cached_data(self, cache_key):
        """获取缓存数据"""
        return cache.get(cache_key)
    
    def set_cached_data(self, cache_key, data, timeout=None):
        """设置缓存数据"""
        timeout = timeout or self.cache_timeout
        cache.set(cache_key, data, timeout)
    
    def generate_cache_key(self, template_id, start_date, end_date, params=None):
        """生成缓存键"""
        key_parts = [f"report", str(template_id), str(start_date), str(end_date)]
        if params:
            key_parts.extend([f"{k}:{v}" for k, v in sorted(params.items())])
        return ":".join(key_parts)
```
