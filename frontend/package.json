{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "@vueuse/core": "^13.6.0", "axios": "^1.11.0", "echarts": "^5.6.0", "element-plus": "^2.10.6", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "vite": "^7.1.0"}}