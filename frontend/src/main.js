import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import router from './router'
import tracker from './utils/tracker'

const app = createApp(App)
const pinia = createPinia()

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 将router绑定到window，供tracker使用
window.$router = router

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err)
  tracker.trackError(err, { componentInfo: info })
}

// 监听路由变化
router.afterEach((to, from) => {
  tracker.trackPageView(to.fullPath)
})

app.use(pinia)
app.use(router)
app.use(ElementPlus)
app.mount('#app')
