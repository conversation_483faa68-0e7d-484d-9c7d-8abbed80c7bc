import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../store/user'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: () => import('../layouts/MainLayout.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('../views/Dashboard.vue'),
        meta: { requiresAuth: true, title: '数据概览' }
      },
      {
        path: 'products/list',
        name: 'ProductList',
        component: () => import('../views/products/ProductList.vue'),
        meta: { requiresAuth: true, title: '商品列表' }
      },
      {
        path: 'products/categories',
        name: 'ProductCategories',
        component: () => import('../views/products/Categories.vue'),
        meta: { requiresAuth: true, title: '商品分类' }
      },
      {
        path: 'orders',
        name: 'OrderList',
        component: () => import('../views/orders/OrderList.vue'),
        meta: { requiresAuth: true, title: '订单管理' }
      },
      {
        path: 'analytics/behavior',
        name: 'UserBehavior',
        component: () => import('../views/analytics/UserBehavior.vue'),
        meta: { requiresAuth: true, title: '用户行为分析' }
      },
      {
        path: 'analytics/product',
        name: 'ProductAnalysis',
        component: () => import('../views/analytics/ProductAnalysis.vue'),
        meta: { requiresAuth: true, title: '商品分析' }
      },
      {
        path: 'analytics/funnel',
        name: 'FunnelAnalysis',
        component: () => import('../views/analytics/FunnelAnalysis.vue'),
        meta: { requiresAuth: true, title: '漏斗分析' }
      },
      {
        path: 'visualization',
        name: 'Visualization',
        component: () => import('../views/visualization/Dashboard.vue'),
        meta: { requiresAuth: true, title: '数据大屏' }
      },
      {
        path: 'reports',
        name: 'Reports',
        component: () => import('../views/reports/ReportList.vue'),
        meta: { requiresAuth: true, title: '报表管理' }
      },
      {
        path: 'system/users',
        name: 'UserManagement',
        component: () => import('../views/system/UserManagement.vue'),
        meta: { requiresAuth: true, requiresAdmin: true, title: '用户管理' }
      },
      {
        path: 'system/config',
        name: 'SystemConfig',
        component: () => import('../views/system/Config.vue'),
        meta: { requiresAuth: true, requiresAdmin: true, title: '系统配置' }
      },
      {
        path: 'system/logs',
        name: 'SystemLogs',
        component: () => import('../views/system/Logs.vue'),
        meta: { requiresAuth: true, requiresAdmin: true, title: '操作日志' }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('../views/Profile.vue'),
        meta: { requiresAuth: true, title: '个人中心' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  const token = localStorage.getItem('token')
  
  if (to.meta.requiresAuth && !token) {
    next('/login')
  } else if ((to.path === '/login' || to.path === '/register') && token) {
    next('/dashboard')
  } else if (to.meta.requiresAdmin && userStore.userRole !== 'admin') {
    next('/dashboard')
  } else {
    next()
  }
})

export default router