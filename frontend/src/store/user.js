import { defineStore } from 'pinia'
import request from '../utils/request'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    user: JSON.parse(localStorage.getItem('user') || '{}')
  }),
  
  getters: {
    isLoggedIn: state => !!state.token,
    username: state => state.user?.username || '',
    userRole: state => state.user?.role || ''
  },
  
  actions: {
    async login(loginData) {
      try {
        const res = await request.post('/auth/login/', loginData)
        this.token = res.data.token
        this.user = res.data.user
        localStorage.setItem('token', res.data.token)
        localStorage.setItem('user', JSON.stringify(res.data.user))
        return res
      } catch (error) {
        throw error
      }
    },
    
    async register(registerData) {
      try {
        const res = await request.post('/auth/register/', registerData)
        this.token = res.data.token
        localStorage.setItem('token', res.data.token)
        return res
      } catch (error) {
        throw error
      }
    },
    
    async logout() {
      try {
        await request.post('/auth/logout/')
      } finally {
        this.token = ''
        this.user = {}
        localStorage.removeItem('token')
        localStorage.removeItem('user')
      }
    },
    
    async fetchUserInfo() {
      try {
        const res = await request.get('/auth/me/')
        this.user = res.data
        localStorage.setItem('user', JSON.stringify(res.data))
        return res
      } catch (error) {
        throw error
      }
    },
    
    async updateProfile(profileData) {
      try {
        const res = await request.put('/auth/profile/', profileData)
        this.user = res.data
        localStorage.setItem('user', JSON.stringify(res.data))
        return res
      } catch (error) {
        throw error
      }
    },
    
    async changePassword(passwordData) {
      try {
        const res = await request.put('/auth/change-password/', passwordData)
        return res
      } catch (error) {
        throw error
      }
    }
  }
})