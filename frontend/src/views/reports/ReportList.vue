<template>
  <div class="report-list">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 报表模板 -->
      <el-tab-pane label="报表模板" name="templates">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>报表模板管理</span>
              <el-button type="primary" @click="showTemplateDialog()">
                <el-icon><Plus /></el-icon> 新建模板
              </el-button>
            </div>
          </template>
          
          <!-- 筛选条件 -->
          <el-form :inline="true" :model="templateFilter" class="filter-form">
            <el-form-item label="模板类型">
              <el-select v-model="templateFilter.template_type" placeholder="全部" clearable>
                <el-option label="日报" value="daily" />
                <el-option label="周报" value="weekly" />
                <el-option label="月报" value="monthly" />
                <el-option label="自定义" value="custom" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="templateFilter.status" placeholder="全部" clearable>
                <el-option label="激活" value="active" />
                <el-option label="停用" value="inactive" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadTemplates">查询</el-button>
              <el-button @click="resetTemplateFilter">重置</el-button>
            </el-form-item>
          </el-form>
          
          <!-- 模板列表 -->
          <el-table :data="templates" v-loading="templateLoading">
            <el-table-column prop="template_name" label="模板名称" min-width="150" />
            <el-table-column prop="template_type" label="类型" width="100">
              <template #default="scope">
                <el-tag :type="getTypeColor(scope.row.template_type)">
                  {{ getTypeLabel(scope.row.template_type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="200" />
            <el-table-column prop="report_count" label="报表数量" width="100" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                  {{ scope.row.status === 'active' ? '激活' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="160">
              <template #default="scope">
                {{ formatDateTime(scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="240" fixed="right">
              <template #default="scope">
                <el-button size="small" @click="previewTemplate(scope.row)">预览</el-button>
                <el-button size="small" type="primary" @click="generateReport(scope.row)">生成</el-button>
                <el-button size="small" @click="showTemplateDialog(scope.row)">编辑</el-button>
                <el-button size="small" type="danger" @click="deleteTemplate(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <el-pagination
            v-model:current-page="templatePage"
            v-model:page-size="templatePageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="templateTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadTemplates"
            @current-change="loadTemplates"
          />
        </el-card>
      </el-tab-pane>
      
      <!-- 报表实例 -->
      <el-tab-pane label="报表实例" name="instances">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>生成的报表</span>
            </div>
          </template>
          
          <!-- 筛选条件 -->
          <el-form :inline="true" :model="instanceFilter" class="filter-form">
            <el-form-item label="模板">
              <el-select v-model="instanceFilter.template_id" placeholder="全部模板" clearable>
                <el-option
                  v-for="template in allTemplates"
                  :key="template.id"
                  :label="template.template_name"
                  :value="template.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="instanceFilter.status" placeholder="全部" clearable>
                <el-option label="待生成" value="pending" />
                <el-option label="生成中" value="generating" />
                <el-option label="已完成" value="completed" />
                <el-option label="失败" value="failed" />
              </el-select>
            </el-form-item>
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="instanceDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleInstanceDateChange"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadInstances">查询</el-button>
              <el-button @click="resetInstanceFilter">重置</el-button>
            </el-form-item>
          </el-form>
          
          <!-- 实例列表 -->
          <el-table :data="instances" v-loading="instanceLoading">
            <el-table-column prop="report_name" label="报表名称" min-width="200" />
            <el-table-column prop="template_name" label="模板" width="150" />
            <el-table-column prop="report_period" label="报表周期" width="180" />
            <el-table-column prop="generation_status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.generation_status)">
                  {{ getStatusLabel(scope.row.generation_status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="file_size" label="文件大小" width="100">
              <template #default="scope">
                {{ formatFileSize(scope.row.file_size) }}
              </template>
            </el-table-column>
            <el-table-column prop="generated_at" label="生成时间" width="160">
              <template #default="scope">
                {{ formatDateTime(scope.row.generated_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180" fixed="right">
              <template #default="scope">
                <el-button 
                  size="small" 
                  @click="viewReport(scope.row)"
                  :disabled="scope.row.generation_status !== 'completed'"
                >
                  查看
                </el-button>
                <el-button 
                  size="small" 
                  type="primary" 
                  @click="downloadReport(scope.row)"
                  :disabled="scope.row.generation_status !== 'completed'"
                >
                  下载
                </el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="deleteInstance(scope.row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <el-pagination
            v-model:current-page="instancePage"
            v-model:page-size="instancePageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="instanceTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadInstances"
            @current-change="loadInstances"
          />
        </el-card>
      </el-tab-pane>
      
      <!-- 我的订阅 -->
      <el-tab-pane label="我的订阅" name="subscriptions">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>报表订阅</span>
              <el-button type="primary" @click="showSubscriptionDialog()">
                <el-icon><Plus /></el-icon> 新增订阅
              </el-button>
            </div>
          </template>
          
          <!-- 订阅列表 -->
          <el-table :data="subscriptions" v-loading="subscriptionLoading">
            <el-table-column prop="template_name" label="报表模板" min-width="150" />
            <el-table-column prop="email" label="接收邮箱" width="200" />
            <el-table-column prop="schedule_type" label="发送频率" width="100">
              <template #default="scope">
                {{ getScheduleLabel(scope.row.schedule_type) }}
              </template>
            </el-table-column>
            <el-table-column prop="schedule_description" label="发送时间" width="200" />
            <el-table-column prop="is_active" label="状态" width="80">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.is_active"
                  @change="toggleSubscription(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="last_sent_at" label="最后发送" width="160">
              <template #default="scope">
                {{ scope.row.last_sent_at ? formatDateTime(scope.row.last_sent_at) : '未发送' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="scope">
                <el-button size="small" type="danger" @click="cancelSubscription(scope.row)">
                  取消订阅
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 生成报表对话框 -->
    <el-dialog v-model="generateDialogVisible" title="生成报表" width="500px">
      <el-form :model="generateForm" label-width="100px">
        <el-form-item label="报表名称">
          <el-input v-model="generateForm.report_name" placeholder="留空则自动生成" />
        </el-form-item>
        <el-form-item label="日期范围" required>
          <el-date-picker
            v-model="generateDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="导出格式">
          <el-radio-group v-model="generateForm.export_format">
            <el-radio value="pdf">PDF</el-radio>
            <el-radio value="excel">Excel</el-radio>
            <el-radio value="html">HTML</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="generateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmGenerate">确定生成</el-button>
      </template>
    </el-dialog>
    
    <!-- 订阅设置对话框 -->
    <el-dialog v-model="subscriptionDialogVisible" title="订阅设置" width="500px">
      <el-form :model="subscriptionForm" label-width="100px">
        <el-form-item label="报表模板" required>
          <el-select v-model="subscriptionForm.template_id" placeholder="请选择模板">
            <el-option
              v-for="template in allTemplates"
              :key="template.id"
              :label="template.template_name"
              :value="template.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="接收邮箱" required>
          <el-input v-model="subscriptionForm.email" placeholder="请输入邮箱地址" />
        </el-form-item>
        <el-form-item label="发送频率" required>
          <el-radio-group v-model="subscriptionForm.schedule_type">
            <el-radio value="daily">每日</el-radio>
            <el-radio value="weekly">每周</el-radio>
            <el-radio value="monthly">每月</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发送时间" required>
          <el-time-picker
            v-model="subscriptionForm.time"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择时间"
          />
        </el-form-item>
        <el-form-item 
          v-if="subscriptionForm.schedule_type === 'weekly'"
          label="每周几"
          required
        >
          <el-select v-model="subscriptionForm.day_of_week">
            <el-option label="周一" :value="1" />
            <el-option label="周二" :value="2" />
            <el-option label="周三" :value="3" />
            <el-option label="周四" :value="4" />
            <el-option label="周五" :value="5" />
            <el-option label="周六" :value="6" />
            <el-option label="周日" :value="7" />
          </el-select>
        </el-form-item>
        <el-form-item 
          v-if="subscriptionForm.schedule_type === 'monthly'"
          label="每月几号"
          required
        >
          <el-input-number v-model="subscriptionForm.day_of_month" :min="1" :max="31" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="subscriptionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSubscription">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import request from '../../utils/request'

// 选项卡
const activeTab = ref('templates')

// 模板相关
const templates = ref([])
const templateLoading = ref(false)
const templatePage = ref(1)
const templatePageSize = ref(20)
const templateTotal = ref(0)
const allTemplates = ref([])
const templateFilter = reactive({
  template_type: '',
  status: ''
})

// 实例相关
const instances = ref([])
const instanceLoading = ref(false)
const instancePage = ref(1)
const instancePageSize = ref(20)
const instanceTotal = ref(0)
const instanceDateRange = ref([])
const instanceFilter = reactive({
  template_id: '',
  status: '',
  start_date: '',
  end_date: ''
})

// 订阅相关
const subscriptions = ref([])
const subscriptionLoading = ref(false)

// 对话框
const generateDialogVisible = ref(false)
const subscriptionDialogVisible = ref(false)
const currentTemplate = ref(null)
const generateDateRange = ref([])
const generateForm = reactive({
  report_name: '',
  export_format: 'pdf'
})

const subscriptionForm = reactive({
  template_id: '',
  email: '',
  schedule_type: 'daily',
  time: '09:00',
  day_of_week: 1,
  day_of_month: 1
})

// 加载模板列表
const loadTemplates = async () => {
  templateLoading.value = true
  try {
    const params = {
      page: templatePage.value,
      limit: templatePageSize.value,
      ...templateFilter
    }
    const res = await request.get('/reports/templates', { params })
    templates.value = res.data.templates || []
    templateTotal.value = res.data.pagination?.total_count || 0
  } catch (error) {
    ElMessage.error('获取模板列表失败')
  } finally {
    templateLoading.value = false
  }
}

// 加载所有模板（用于下拉选择）
const loadAllTemplates = async () => {
  try {
    const res = await request.get('/reports/templates', {
      params: { limit: 100, status: 'active' }
    })
    allTemplates.value = res.data.templates || []
  } catch (error) {
    console.error('获取模板列表失败', error)
  }
}

// 加载实例列表
const loadInstances = async () => {
  instanceLoading.value = true
  try {
    const params = {
      page: instancePage.value,
      limit: instancePageSize.value,
      ...instanceFilter
    }
    const res = await request.get('/reports/instances', { params })
    instances.value = res.data.results || []
    instanceTotal.value = res.data.count || 0
  } catch (error) {
    ElMessage.error('获取报表列表失败')
  } finally {
    instanceLoading.value = false
  }
}

// 加载订阅列表
const loadSubscriptions = async () => {
  subscriptionLoading.value = true
  try {
    const res = await request.get('/reports/subscriptions/my')
    subscriptions.value = res.data.subscriptions || []
  } catch (error) {
    ElMessage.error('获取订阅列表失败')
  } finally {
    subscriptionLoading.value = false
  }
}

// 生成报表
const generateReport = (template) => {
  currentTemplate.value = template
  generateForm.report_name = ''
  generateForm.export_format = 'pdf'
  generateDateRange.value = []
  generateDialogVisible.value = true
}

// 确认生成
const confirmGenerate = async () => {
  if (!generateDateRange.value || generateDateRange.value.length !== 2) {
    ElMessage.warning('请选择日期范围')
    return
  }
  
  try {
    const data = {
      template_id: currentTemplate.value.id,
      start_date: generateDateRange.value[0],
      end_date: generateDateRange.value[1],
      report_name: generateForm.report_name,
      export_format: generateForm.export_format
    }
    
    const res = await request.post(`/reports/templates/${currentTemplate.value.id}/generate`, data)
    ElMessage.success('报表生成任务已创建')
    generateDialogVisible.value = false
    
    // 切换到实例标签页
    activeTab.value = 'instances'
    loadInstances()
  } catch (error) {
    ElMessage.error('生成报表失败')
  }
}

// 预览模板
const previewTemplate = async (template) => {
  ElMessage.info('预览功能开发中')
}

// 显示模板对话框
const showTemplateDialog = (template = null) => {
  ElMessage.info('模板编辑功能开发中')
}

// 删除模板
const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm('确定要删除该模板吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await request.delete(`/reports/templates/${template.id}`)
    ElMessage.success('删除成功')
    loadTemplates()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 查看报表
const viewReport = async (instance) => {
  try {
    const res = await request.get(`/reports/instances/${instance.id}/data`)
    // 这里可以打开一个新窗口或对话框显示报表内容
    console.log('报表数据', res.data)
    ElMessage.info('查看功能开发中')
  } catch (error) {
    ElMessage.error('获取报表数据失败')
  }
}

// 下载报表
const downloadReport = async (instance) => {
  try {
    window.open(`/api/reports/instances/${instance.id}/download`, '_blank')
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

// 删除实例
const deleteInstance = async (instance) => {
  try {
    await ElMessageBox.confirm('确定要删除该报表吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await request.delete(`/reports/instances/${instance.id}`)
    ElMessage.success('删除成功')
    loadInstances()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 显示订阅对话框
const showSubscriptionDialog = () => {
  subscriptionForm.template_id = ''
  subscriptionForm.email = ''
  subscriptionForm.schedule_type = 'daily'
  subscriptionForm.time = '09:00'
  subscriptionForm.day_of_week = 1
  subscriptionForm.day_of_month = 1
  subscriptionDialogVisible.value = true
}

// 确认订阅
const confirmSubscription = async () => {
  if (!subscriptionForm.template_id || !subscriptionForm.email) {
    ElMessage.warning('请填写必要信息')
    return
  }
  
  try {
    const schedule_config = {
      time: subscriptionForm.time
    }
    
    if (subscriptionForm.schedule_type === 'weekly') {
      schedule_config.day_of_week = subscriptionForm.day_of_week
    } else if (subscriptionForm.schedule_type === 'monthly') {
      schedule_config.day_of_month = subscriptionForm.day_of_month
    }
    
    const data = {
      template_id: subscriptionForm.template_id,
      email: subscriptionForm.email,
      schedule_type: subscriptionForm.schedule_type,
      schedule_config
    }
    
    await request.post('/reports/subscriptions', data)
    ElMessage.success('订阅成功')
    subscriptionDialogVisible.value = false
    loadSubscriptions()
  } catch (error) {
    ElMessage.error('订阅失败')
  }
}

// 切换订阅状态
const toggleSubscription = async (subscription) => {
  try {
    await request.put(`/reports/subscriptions/${subscription.id}`, {
      is_active: subscription.is_active
    })
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
    subscription.is_active = !subscription.is_active
  }
}

// 取消订阅
const cancelSubscription = async (subscription) => {
  try {
    await ElMessageBox.confirm('确定要取消该订阅吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await request.delete(`/reports/subscriptions/${subscription.id}`)
    ElMessage.success('取消订阅成功')
    loadSubscriptions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消订阅失败')
    }
  }
}

// 处理日期变化
const handleInstanceDateChange = (value) => {
  if (value) {
    instanceFilter.start_date = value[0]
    instanceFilter.end_date = value[1]
  } else {
    instanceFilter.start_date = ''
    instanceFilter.end_date = ''
  }
}

// 重置筛选
const resetTemplateFilter = () => {
  templateFilter.template_type = ''
  templateFilter.status = ''
  loadTemplates()
}

const resetInstanceFilter = () => {
  instanceFilter.template_id = ''
  instanceFilter.status = ''
  instanceFilter.start_date = ''
  instanceFilter.end_date = ''
  instanceDateRange.value = []
  loadInstances()
}

// 处理标签页切换
const handleTabClick = (tab) => {
  if (tab.props.name === 'templates') {
    loadTemplates()
  } else if (tab.props.name === 'instances') {
    loadInstances()
  } else if (tab.props.name === 'subscriptions') {
    loadSubscriptions()
  }
}

// 工具函数
const getTypeLabel = (type) => {
  const labels = {
    'daily': '日报',
    'weekly': '周报',
    'monthly': '月报',
    'custom': '自定义'
  }
  return labels[type] || type
}

const getTypeColor = (type) => {
  const colors = {
    'daily': 'primary',
    'weekly': 'success',
    'monthly': 'warning',
    'custom': 'info'
  }
  return colors[type] || 'info'
}

const getStatusType = (status) => {
  const types = {
    'pending': 'warning',
    'generating': 'primary',
    'completed': 'success',
    'failed': 'danger'
  }
  return types[status] || 'info'
}

const getStatusLabel = (status) => {
  const labels = {
    'pending': '待生成',
    'generating': '生成中',
    'completed': '已完成',
    'failed': '失败'
  }
  return labels[status] || status
}

const getScheduleLabel = (type) => {
  const labels = {
    'daily': '每日',
    'weekly': '每周',
    'monthly': '每月'
  }
  return labels[type] || type
}

const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatFileSize = (size) => {
  if (!size) return '-'
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(2) + ' KB'
  if (size < 1024 * 1024 * 1024) return (size / 1024 / 1024).toFixed(2) + ' MB'
  return (size / 1024 / 1024 / 1024).toFixed(2) + ' GB'
}

onMounted(() => {
  loadTemplates()
  loadAllTemplates()
})
</script>

<style scoped>
.report-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-form {
  margin-bottom: 20px;
}

.el-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>