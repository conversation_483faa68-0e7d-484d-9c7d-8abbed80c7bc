<template>
  <div class="system-config">
    <el-tabs v-model="activeTab">
      <!-- 基础配置 -->
      <el-tab-pane label="基础配置" name="basic">
        <el-card>
          <el-form :model="basicConfig" label-width="120px">
            <el-form-item label="系统名称">
              <el-input v-model="basicConfig.system_name" placeholder="请输入系统名称" />
            </el-form-item>
            <el-form-item label="系统版本">
              <el-input v-model="basicConfig.system_version" disabled />
            </el-form-item>
            <el-form-item label="系统描述">
              <el-input v-model="basicConfig.system_desc" type="textarea" :rows="3" placeholder="请输入系统描述" />
            </el-form-item>
            <el-form-item label="维护模式">
              <el-switch v-model="basicConfig.maintenance_mode" />
              <span class="ml-2 text-gray-500">开启后普通用户无法访问系统</span>
            </el-form-item>
            <el-form-item label="维护提示">
              <el-input 
                v-model="basicConfig.maintenance_message" 
                type="textarea" 
                :rows="2"
                placeholder="系统维护中，请稍后访问"
                :disabled="!basicConfig.maintenance_mode"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveBasicConfig">保存配置</el-button>
              <el-button @click="resetBasicConfig">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
      
      <!-- 数据采集配置 -->
      <el-tab-pane label="数据采集" name="collection">
        <el-card>
          <el-form :model="collectionConfig" label-width="150px">
            <el-form-item label="启用数据采集">
              <el-switch v-model="collectionConfig.enabled" />
            </el-form-item>
            <el-form-item label="采集频率">
              <el-input-number v-model="collectionConfig.frequency" :min="5" :max="300" /> 秒
            </el-form-item>
            <el-form-item label="批量发送阈值">
              <el-input-number v-model="collectionConfig.batch_size" :min="1" :max="100" /> 条
            </el-form-item>
            <el-form-item label="采集页面访问">
              <el-switch v-model="collectionConfig.collect_pageview" />
            </el-form-item>
            <el-form-item label="采集点击事件">
              <el-switch v-model="collectionConfig.collect_click" />
            </el-form-item>
            <el-form-item label="采集搜索行为">
              <el-switch v-model="collectionConfig.collect_search" />
            </el-form-item>
            <el-form-item label="采集错误日志">
              <el-switch v-model="collectionConfig.collect_error" />
            </el-form-item>
            <el-form-item label="忽略的URL">
              <el-input 
                v-model="collectionConfig.ignore_urls" 
                type="textarea" 
                :rows="3"
                placeholder="每行一个URL规则，支持通配符*"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveCollectionConfig">保存配置</el-button>
              <el-button @click="resetCollectionConfig">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
      
      <!-- 邮件配置 -->
      <el-tab-pane label="邮件配置" name="email">
        <el-card>
          <el-form :model="emailConfig" label-width="120px">
            <el-form-item label="SMTP服务器">
              <el-input v-model="emailConfig.smtp_server" placeholder="smtp.example.com" />
            </el-form-item>
            <el-form-item label="SMTP端口">
              <el-input-number v-model="emailConfig.smtp_port" :min="1" :max="65535" />
            </el-form-item>
            <el-form-item label="启用SSL">
              <el-switch v-model="emailConfig.smtp_ssl" />
            </el-form-item>
            <el-form-item label="发件人邮箱">
              <el-input v-model="emailConfig.from_email" placeholder="<EMAIL>" />
            </el-form-item>
            <el-form-item label="发件人名称">
              <el-input v-model="emailConfig.from_name" placeholder="电商分析系统" />
            </el-form-item>
            <el-form-item label="SMTP用户名">
              <el-input v-model="emailConfig.smtp_username" placeholder="请输入SMTP用户名" />
            </el-form-item>
            <el-form-item label="SMTP密码">
              <el-input v-model="emailConfig.smtp_password" type="password" placeholder="请输入SMTP密码" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveEmailConfig">保存配置</el-button>
              <el-button @click="testEmailConfig">测试连接</el-button>
              <el-button @click="resetEmailConfig">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
      
      <!-- 存储配置 -->
      <el-tab-pane label="存储配置" name="storage">
        <el-card>
          <el-form :model="storageConfig" label-width="150px">
            <el-form-item label="存储类型">
              <el-radio-group v-model="storageConfig.storage_type">
                <el-radio value="local">本地存储</el-radio>
                <el-radio value="oss">阿里云OSS</el-radio>
                <el-radio value="cos">腾讯云COS</el-radio>
                <el-radio value="s3">AWS S3</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <template v-if="storageConfig.storage_type === 'local'">
              <el-form-item label="存储路径">
                <el-input v-model="storageConfig.local_path" placeholder="/data/uploads" />
              </el-form-item>
              <el-form-item label="访问URL前缀">
                <el-input v-model="storageConfig.local_url_prefix" placeholder="http://localhost:8000/uploads" />
              </el-form-item>
            </template>
            
            <template v-else>
              <el-form-item label="Access Key">
                <el-input v-model="storageConfig.access_key" placeholder="请输入Access Key" />
              </el-form-item>
              <el-form-item label="Secret Key">
                <el-input v-model="storageConfig.secret_key" type="password" placeholder="请输入Secret Key" />
              </el-form-item>
              <el-form-item label="Bucket名称">
                <el-input v-model="storageConfig.bucket_name" placeholder="请输入Bucket名称" />
              </el-form-item>
              <el-form-item label="区域/端点">
                <el-input v-model="storageConfig.endpoint" placeholder="请输入区域或端点" />
              </el-form-item>
            </template>
            
            <el-form-item label="最大文件大小">
              <el-input-number v-model="storageConfig.max_file_size" :min="1" :max="1024" /> MB
            </el-form-item>
            <el-form-item label="允许的文件类型">
              <el-select v-model="storageConfig.allowed_types" multiple placeholder="选择允许的文件类型">
                <el-option label="图片" value="image" />
                <el-option label="文档" value="document" />
                <el-option label="视频" value="video" />
                <el-option label="压缩包" value="archive" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveStorageConfig">保存配置</el-button>
              <el-button @click="testStorageConfig">测试连接</el-button>
              <el-button @click="resetStorageConfig">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
      
      <!-- 性能配置 -->
      <el-tab-pane label="性能配置" name="performance">
        <el-card>
          <el-form :model="performanceConfig" label-width="150px">
            <el-form-item label="启用缓存">
              <el-switch v-model="performanceConfig.cache_enabled" />
            </el-form-item>
            <el-form-item label="缓存类型">
              <el-radio-group v-model="performanceConfig.cache_type" :disabled="!performanceConfig.cache_enabled">
                <el-radio value="memory">内存缓存</el-radio>
                <el-radio value="redis">Redis缓存</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="缓存过期时间">
              <el-input-number v-model="performanceConfig.cache_ttl" :min="60" :max="86400" /> 秒
            </el-form-item>
            <el-form-item label="数据压缩">
              <el-switch v-model="performanceConfig.compression_enabled" />
            </el-form-item>
            <el-form-item label="分页大小">
              <el-input-number v-model="performanceConfig.page_size" :min="10" :max="100" />
            </el-form-item>
            <el-form-item label="查询超时">
              <el-input-number v-model="performanceConfig.query_timeout" :min="5" :max="300" /> 秒
            </el-form-item>
            <el-form-item label="并发限制">
              <el-input-number v-model="performanceConfig.concurrency_limit" :min="10" :max="1000" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="savePerformanceConfig">保存配置</el-button>
              <el-button @click="clearCache">清理缓存</el-button>
              <el-button @click="resetPerformanceConfig">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import request from '../../utils/request'

const activeTab = ref('basic')

// 基础配置
const basicConfig = reactive({
  system_name: '电商用户行为分析系统',
  system_version: 'v1.0.0',
  system_desc: '',
  maintenance_mode: false,
  maintenance_message: '系统维护中，请稍后访问'
})

// 数据采集配置
const collectionConfig = reactive({
  enabled: true,
  frequency: 30,
  batch_size: 10,
  collect_pageview: true,
  collect_click: true,
  collect_search: true,
  collect_error: true,
  ignore_urls: ''
})

// 邮件配置
const emailConfig = reactive({
  smtp_server: '',
  smtp_port: 587,
  smtp_ssl: true,
  from_email: '',
  from_name: '',
  smtp_username: '',
  smtp_password: ''
})

// 存储配置
const storageConfig = reactive({
  storage_type: 'local',
  local_path: '/data/uploads',
  local_url_prefix: '',
  access_key: '',
  secret_key: '',
  bucket_name: '',
  endpoint: '',
  max_file_size: 10,
  allowed_types: ['image', 'document']
})

// 性能配置
const performanceConfig = reactive({
  cache_enabled: true,
  cache_type: 'memory',
  cache_ttl: 3600,
  compression_enabled: true,
  page_size: 20,
  query_timeout: 30,
  concurrency_limit: 100
})

// 保存基础配置
const saveBasicConfig = async () => {
  try {
    await request.post('/system/configs/basic', basicConfig)
    ElMessage.success('基础配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 重置基础配置
const resetBasicConfig = () => {
  basicConfig.system_name = '电商用户行为分析系统'
  basicConfig.system_desc = ''
  basicConfig.maintenance_mode = false
  basicConfig.maintenance_message = '系统维护中，请稍后访问'
}

// 保存数据采集配置
const saveCollectionConfig = async () => {
  try {
    await request.post('/system/configs/collection', collectionConfig)
    ElMessage.success('数据采集配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 重置数据采集配置
const resetCollectionConfig = () => {
  collectionConfig.enabled = true
  collectionConfig.frequency = 30
  collectionConfig.batch_size = 10
  collectionConfig.collect_pageview = true
  collectionConfig.collect_click = true
  collectionConfig.collect_search = true
  collectionConfig.collect_error = true
  collectionConfig.ignore_urls = ''
}

// 保存邮件配置
const saveEmailConfig = async () => {
  try {
    await request.post('/system/configs/email', emailConfig)
    ElMessage.success('邮件配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 测试邮件配置
const testEmailConfig = async () => {
  try {
    await request.post('/system/configs/email/test', emailConfig)
    ElMessage.success('邮件服务器连接成功')
  } catch (error) {
    ElMessage.error('连接失败，请检查配置')
  }
}

// 重置邮件配置
const resetEmailConfig = () => {
  emailConfig.smtp_server = ''
  emailConfig.smtp_port = 587
  emailConfig.smtp_ssl = true
  emailConfig.from_email = ''
  emailConfig.from_name = ''
  emailConfig.smtp_username = ''
  emailConfig.smtp_password = ''
}

// 保存存储配置
const saveStorageConfig = async () => {
  try {
    await request.post('/system/configs/storage', storageConfig)
    ElMessage.success('存储配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 测试存储配置
const testStorageConfig = async () => {
  try {
    await request.post('/system/configs/storage/test', storageConfig)
    ElMessage.success('存储服务连接成功')
  } catch (error) {
    ElMessage.error('连接失败，请检查配置')
  }
}

// 重置存储配置
const resetStorageConfig = () => {
  storageConfig.storage_type = 'local'
  storageConfig.local_path = '/data/uploads'
  storageConfig.local_url_prefix = ''
  storageConfig.access_key = ''
  storageConfig.secret_key = ''
  storageConfig.bucket_name = ''
  storageConfig.endpoint = ''
  storageConfig.max_file_size = 10
  storageConfig.allowed_types = ['image', 'document']
}

// 保存性能配置
const savePerformanceConfig = async () => {
  try {
    await request.post('/system/configs/performance', performanceConfig)
    ElMessage.success('性能配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 清理缓存
const clearCache = async () => {
  try {
    await request.post('/system/cache/clear')
    ElMessage.success('缓存清理成功')
  } catch (error) {
    ElMessage.error('清理失败')
  }
}

// 重置性能配置
const resetPerformanceConfig = () => {
  performanceConfig.cache_enabled = true
  performanceConfig.cache_type = 'memory'
  performanceConfig.cache_ttl = 3600
  performanceConfig.compression_enabled = true
  performanceConfig.page_size = 20
  performanceConfig.query_timeout = 30
  performanceConfig.concurrency_limit = 100
}

// 加载配置
const loadConfigs = async () => {
  try {
    const res = await request.get('/system/configs')
    // 这里应该根据实际返回的数据更新各个配置
    console.log('加载配置', res.data)
  } catch (error) {
    console.error('加载配置失败', error)
  }
}

onMounted(() => {
  loadConfigs()
})
</script>

<style scoped>
.system-config {
  padding: 20px;
}

.ml-2 {
  margin-left: 8px;
}

.text-gray-500 {
  color: #6b7280;
}

.el-form-item {
  margin-bottom: 22px;
}
</style>