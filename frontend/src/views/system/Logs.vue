<template>
  <div class="system-logs">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>操作日志</span>
          <div>
            <el-button @click="refreshLogs">
              <el-icon><Refresh /></el-icon> 刷新
            </el-button>
            <el-button type="primary" @click="exportLogs">
              <el-icon><Download /></el-icon> 导出日志
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 筛选条件 -->
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="操作类型">
          <el-select v-model="filterForm.operation_type" placeholder="全部" clearable>
            <el-option label="登录" value="login" />
            <el-option label="登出" value="logout" />
            <el-option label="创建" value="create" />
            <el-option label="更新" value="update" />
            <el-option label="删除" value="delete" />
            <el-option label="查询" value="query" />
            <el-option label="导出" value="export" />
            <el-option label="导入" value="import" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作模块">
          <el-select v-model="filterForm.operation_module" placeholder="全部" clearable>
            <el-option label="用户管理" value="user" />
            <el-option label="商品管理" value="product" />
            <el-option label="订单管理" value="order" />
            <el-option label="数据分析" value="analytics" />
            <el-option label="报表管理" value="report" />
            <el-option label="系统管理" value="system" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作人">
          <el-input v-model="filterForm.username" placeholder="请输入用户名" clearable />
        </el-form-item>
        <el-form-item label="IP地址">
          <el-input v-model="filterForm.ip_address" placeholder="请输入IP地址" clearable />
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 统计信息 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-value">{{ stats.totalLogs }}</div>
            <div class="stat-label">总日志数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-value">{{ stats.todayLogs }}</div>
            <div class="stat-label">今日日志</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-value">{{ stats.activeUsers }}</div>
            <div class="stat-label">活跃用户</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-value">{{ stats.errorLogs }}</div>
            <div class="stat-label">错误日志</div>
          </div>
        </el-col>
      </el-row>
      
      <!-- 日志列表 -->
      <el-table :data="logs" v-loading="loading" style="width: 100%" :row-class-name="getRowClassName">
        <el-table-column type="expand">
          <template #default="props">
            <div class="log-detail">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="请求URL">{{ props.row.request_url }}</el-descriptions-item>
                <el-descriptions-item label="请求方法">{{ props.row.request_method }}</el-descriptions-item>
                <el-descriptions-item label="用户代理" :span="2">{{ props.row.user_agent }}</el-descriptions-item>
                <el-descriptions-item label="请求参数" :span="2">
                  <pre>{{ formatJson(props.row.request_params) }}</pre>
                </el-descriptions-item>
                <el-descriptions-item label="响应状态">
                  <el-tag :type="getStatusType(props.row.response_code)">{{ props.row.response_code }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="执行时间">{{ props.row.execution_time }}ms</el-descriptions-item>
              </el-descriptions>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="操作人" width="120">
          <template #default="scope">
            <el-tag size="small">{{ scope.row.username || '系统' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operation_type" label="操作类型" width="100">
          <template #default="scope">
            <el-tag :type="getOperationType(scope.row.operation_type)">
              {{ getOperationLabel(scope.row.operation_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operation_module" label="操作模块" width="120">
          <template #default="scope">
            {{ getModuleLabel(scope.row.operation_module) }}
          </template>
        </el-table-column>
        <el-table-column prop="operation_desc" label="操作描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="ip_address" label="IP地址" width="130" />
        <el-table-column prop="response_code" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.response_code)" size="small">
              {{ scope.row.response_code }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="execution_time" label="耗时" width="100">
          <template #default="scope">
            <span :class="getTimeClass(scope.row.execution_time)">
              {{ scope.row.execution_time }}ms
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="操作时间" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="viewDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[20, 50, 100, 200]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    
    <!-- 日志详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="日志详情" width="800px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="日志ID">{{ currentLog.id }}</el-descriptions-item>
        <el-descriptions-item label="操作人">{{ currentLog.username || '系统' }}</el-descriptions-item>
        <el-descriptions-item label="操作类型">
          <el-tag :type="getOperationType(currentLog.operation_type)">
            {{ getOperationLabel(currentLog.operation_type) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作模块">{{ getModuleLabel(currentLog.operation_module) }}</el-descriptions-item>
        <el-descriptions-item label="操作描述" :span="2">{{ currentLog.operation_desc }}</el-descriptions-item>
        <el-descriptions-item label="请求方法">{{ currentLog.request_method }}</el-descriptions-item>
        <el-descriptions-item label="请求URL">{{ currentLog.request_url }}</el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ currentLog.ip_address }}</el-descriptions-item>
        <el-descriptions-item label="响应状态">
          <el-tag :type="getStatusType(currentLog.response_code)">{{ currentLog.response_code }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="执行时间">{{ currentLog.execution_time }}ms</el-descriptions-item>
        <el-descriptions-item label="操作时间">{{ formatDateTime(currentLog.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="用户代理" :span="2">{{ currentLog.user_agent }}</el-descriptions-item>
      </el-descriptions>
      
      <el-divider>请求参数</el-divider>
      <div class="json-viewer">
        <pre>{{ formatJson(currentLog.request_params) }}</pre>
      </div>
      
      <el-divider>响应内容</el-divider>
      <div class="json-viewer">
        <pre>{{ formatJson(currentLog.response_data) }}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Download } from '@element-plus/icons-vue'
import request from '../../utils/request'

// 数据
const logs = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const dateRange = ref([])
const currentLog = ref({})
const detailDialogVisible = ref(false)

// 筛选条件
const filterForm = reactive({
  operation_type: '',
  operation_module: '',
  username: '',
  ip_address: '',
  start_time: '',
  end_time: ''
})

// 统计数据
const stats = reactive({
  totalLogs: 0,
  todayLogs: 0,
  activeUsers: 0,
  errorLogs: 0
})

// 处理日期变化
const handleDateChange = (value) => {
  if (value) {
    filterForm.start_time = value[0]
    filterForm.end_time = value[1]
  } else {
    filterForm.start_time = ''
    filterForm.end_time = ''
  }
}

// 重置筛选
const resetFilter = () => {
  filterForm.operation_type = ''
  filterForm.operation_module = ''
  filterForm.username = ''
  filterForm.ip_address = ''
  filterForm.start_time = ''
  filterForm.end_time = ''
  dateRange.value = []
  handleSearch()
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchLogs()
}

// 刷新
const refreshLogs = () => {
  fetchLogs()
  fetchStats()
}

// 获取日志列表
const fetchLogs = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...filterForm
    }
    const res = await request.get('/system/logs', { params })
    logs.value = res.data.logs || []
    total.value = res.data.total || 0
  } catch (error) {
    ElMessage.error('获取日志列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const res = await request.get('/system/logs/stats')
    Object.assign(stats, res.data)
  } catch (error) {
    console.error('获取统计数据失败', error)
  }
}

// 分页
const handleSizeChange = () => {
  currentPage.value = 1
  fetchLogs()
}

const handleCurrentChange = () => {
  fetchLogs()
}

// 查看详情
const viewDetail = (row) => {
  currentLog.value = row
  detailDialogVisible.value = true
}

// 导出日志
const exportLogs = async () => {
  try {
    const params = {
      ...filterForm,
      export: true
    }
    const res = await request.get('/system/logs/export', {
      params,
      responseType: 'blob'
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([res.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `operation_logs_${new Date().getTime()}.xlsx`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 获取行样式
const getRowClassName = ({ row }) => {
  if (row.response_code >= 400) {
    return 'error-row'
  }
  if (row.execution_time > 1000) {
    return 'warning-row'
  }
  return ''
}

// 获取操作类型样式
const getOperationType = (type) => {
  const types = {
    'login': 'success',
    'logout': 'info',
    'create': 'primary',
    'update': 'warning',
    'delete': 'danger',
    'query': '',
    'export': 'success',
    'import': 'primary'
  }
  return types[type] || ''
}

// 获取操作标签
const getOperationLabel = (type) => {
  const labels = {
    'login': '登录',
    'logout': '登出',
    'create': '创建',
    'update': '更新',
    'delete': '删除',
    'query': '查询',
    'export': '导出',
    'import': '导入'
  }
  return labels[type] || type
}

// 获取模块标签
const getModuleLabel = (module) => {
  const labels = {
    'user': '用户管理',
    'product': '商品管理',
    'order': '订单管理',
    'analytics': '数据分析',
    'report': '报表管理',
    'system': '系统管理'
  }
  return labels[module] || module
}

// 获取状态类型
const getStatusType = (code) => {
  if (code >= 200 && code < 300) return 'success'
  if (code >= 300 && code < 400) return 'warning'
  if (code >= 400 && code < 500) return 'danger'
  if (code >= 500) return 'danger'
  return 'info'
}

// 获取时间样式
const getTimeClass = (time) => {
  if (time > 3000) return 'text-danger'
  if (time > 1000) return 'text-warning'
  return 'text-success'
}

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化JSON
const formatJson = (data) => {
  if (!data) return ''
  if (typeof data === 'string') {
    try {
      data = JSON.parse(data)
    } catch (e) {
      return data
    }
  }
  return JSON.stringify(data, null, 2)
}

onMounted(() => {
  fetchLogs()
  fetchStats()
})
</script>

<style scoped>
.system-logs {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-form {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  margin-top: 5px;
  font-size: 14px;
  color: #909399;
}

.log-detail {
  padding: 20px;
}

.json-viewer {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  max-height: 400px;
  overflow: auto;
}

.json-viewer pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
}

.text-success {
  color: #67c23a;
}

.text-warning {
  color: #e6a23c;
}

.text-danger {
  color: #f56c6c;
}

.el-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 表格行样式 */
:deep(.error-row) {
  background-color: #fef0f0;
}

:deep(.warning-row) {
  background-color: #fdf6ec;
}
</style>