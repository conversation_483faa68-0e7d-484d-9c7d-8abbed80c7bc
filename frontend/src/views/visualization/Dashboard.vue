<template>
  <div class="dashboard-container">
    <!-- 头部信息 -->
    <div class="dashboard-header">
      <div class="header-left">
        <h1 class="title">电商数据分析大屏</h1>
        <div class="time">{{ currentTime }}</div>
      </div>
      <div class="header-center">
        <div class="stat-card">
          <div class="stat-value">{{ totalUsers }}</div>
          <div class="stat-label">总用户数</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ todayOrders }}</div>
          <div class="stat-label">今日订单</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">¥{{ todayRevenue }}</div>
          <div class="stat-label">今日营收</div>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="toggleFullScreen" circle>
          <el-icon><FullScreen /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="dashboard-content">
      <!-- 左侧 -->
      <div class="content-left">
        <!-- 实时订单 -->
        <div class="chart-box">
          <div class="chart-header">
            <span class="chart-title">实时订单</span>
          </div>
          <div class="order-list">
            <div v-for="order in recentOrders" :key="order.id" class="order-item">
              <span class="order-time">{{ formatTime(order.created_at) }}</span>
              <span class="order-user">{{ order.user_name }}</span>
              <span class="order-amount">¥{{ order.total_amount }}</span>
            </div>
          </div>
        </div>
        
        <!-- 热门商品排行 -->
        <div class="chart-box">
          <div class="chart-header">
            <span class="chart-title">热门商品TOP10</span>
          </div>
          <div ref="hotProductsChart" class="chart-content"></div>
        </div>
      </div>

      <!-- 中间 -->
      <div class="content-center">
        <!-- 地图 -->
        <div class="chart-box map-box">
          <div class="chart-header">
            <span class="chart-title">用户地域分布</span>
          </div>
          <div ref="mapChart" class="chart-content"></div>
        </div>
        
        <!-- 销售趋势 -->
        <div class="chart-box">
          <div class="chart-header">
            <span class="chart-title">24小时销售趋势</span>
          </div>
          <div ref="salesTrendChart" class="chart-content"></div>
        </div>
      </div>

      <!-- 右侧 -->
      <div class="content-right">
        <!-- 转化漏斗 -->
        <div class="chart-box">
          <div class="chart-header">
            <span class="chart-title">转化漏斗</span>
          </div>
          <div ref="funnelChart" class="chart-content"></div>
        </div>
        
        <!-- 用户活跃度 -->
        <div class="chart-box">
          <div class="chart-header">
            <span class="chart-title">用户活跃度</span>
          </div>
          <div ref="activeUsersChart" class="chart-content"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import 'echarts/extension/bmap/bmap'
import request from '../../utils/request'
import { FullScreen } from '@element-plus/icons-vue'

// 数据
const currentTime = ref('')
const totalUsers = ref(0)
const todayOrders = ref(0)
const todayRevenue = ref(0)
const recentOrders = ref([])

// 图表实例
let hotProductsChartInstance = null
let mapChartInstance = null
let salesTrendChartInstance = null
let funnelChartInstance = null
let activeUsersChartInstance = null

// 图表ref
const hotProductsChart = ref(null)
const mapChart = ref(null)
const salesTrendChart = ref(null)
const funnelChart = ref(null)
const activeUsersChart = ref(null)

// 定时器
let timer = null
let dataTimer = null

// 更新时间
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化时间
const formatTime = (timeStr) => {
  const date = new Date(timeStr)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 全屏切换
const toggleFullScreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

// 初始化热门商品图表
const initHotProductsChart = () => {
  hotProductsChartInstance = echarts.init(hotProductsChart.value)
  const option = {
    grid: {
      left: '5%',
      right: '5%',
      bottom: '5%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#90979c'
        }
      },
      splitLine: {
        show: false
      },
      axisLabel: {
        color: '#fff'
      }
    },
    yAxis: {
      type: 'category',
      data: [],
      inverse: true,
      axisLine: {
        lineStyle: {
          color: '#90979c'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    series: [{
      name: '销量',
      type: 'bar',
      data: [],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#00c6fb' },
          { offset: 1, color: '#005bea' }
        ])
      },
      label: {
        show: true,
        position: 'right',
        color: '#fff'
      }
    }]
  }
  hotProductsChartInstance.setOption(option)
}

// 初始化地图
const initMapChart = () => {
  mapChartInstance = echarts.init(mapChart.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    visualMap: {
      min: 0,
      max: 1000,
      left: 'left',
      top: 'bottom',
      text: ['高', '低'],
      calculable: true,
      inRange: {
        color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
      },
      textStyle: {
        color: '#fff'
      }
    },
    geo: {
      map: 'china',
      roam: false,
      label: {
        show: false
      },
      itemStyle: {
        areaColor: '#2e72bd',
        borderColor: '#333'
      },
      emphasis: {
        itemStyle: {
          areaColor: '#2a333d'
        }
      }
    },
    series: [{
      name: '用户数',
      type: 'scatter',
      coordinateSystem: 'geo',
      data: [],
      symbolSize: function (val) {
        return val[2] / 10
      },
      encode: {
        value: 2
      },
      label: {
        formatter: '{b}',
        position: 'right',
        show: false
      },
      itemStyle: {
        color: '#ffeb7b'
      },
      emphasis: {
        label: {
          show: true
        }
      }
    }]
  }
  mapChartInstance.setOption(option)
}

// 初始化销售趋势图
const initSalesTrendChart = () => {
  salesTrendChartInstance = echarts.init(salesTrendChart.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: Array.from({ length: 24 }, (_, i) => `${i}:00`),
      axisLine: {
        lineStyle: {
          color: '#90979c'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#90979c'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    series: [{
      name: '销售额',
      type: 'line',
      smooth: true,
      data: [],
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(0, 216, 135, 0.4)' },
          { offset: 1, color: 'rgba(0, 216, 135, 0.1)' }
        ])
      },
      itemStyle: {
        color: '#00d887'
      },
      lineStyle: {
        width: 2
      }
    }]
  }
  salesTrendChartInstance.setOption(option)
}

// 初始化漏斗图
const initFunnelChart = () => {
  funnelChartInstance = echarts.init(funnelChart.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}%'
    },
    series: [{
      name: '转化率',
      type: 'funnel',
      left: '10%',
      top: 20,
      bottom: 20,
      width: '80%',
      min: 0,
      max: 100,
      minSize: '0%',
      maxSize: '100%',
      sort: 'descending',
      gap: 2,
      label: {
        show: true,
        position: 'inside',
        color: '#fff'
      },
      labelLine: {
        length: 10,
        lineStyle: {
          width: 1,
          type: 'solid'
        }
      },
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 1
      },
      emphasis: {
        label: {
          fontSize: 20
        }
      },
      data: []
    }]
  }
  funnelChartInstance.setOption(option)
}

// 初始化用户活跃度图表
const initActiveUsersChart = () => {
  activeUsersChartInstance = echarts.init(activeUsersChart.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      axisLine: {
        lineStyle: {
          color: '#90979c'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#90979c'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    series: [{
      name: '活跃用户',
      type: 'bar',
      data: [],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#ff9800' },
          { offset: 1, color: '#f44336' }
        ])
      }
    }]
  }
  activeUsersChartInstance.setOption(option)
}

// 获取数据
const fetchData = async () => {
  try {
    // 获取统计数据
    const statsRes = await request.get('/analytics/dashboard-stats')
    totalUsers.value = statsRes.data.total_users || 0
    todayOrders.value = statsRes.data.today_orders || 0
    todayRevenue.value = statsRes.data.today_revenue || 0
    recentOrders.value = statsRes.data.recent_orders || []
    
    // 更新热门商品
    if (statsRes.data.hot_products) {
      const products = statsRes.data.hot_products
      hotProductsChartInstance.setOption({
        yAxis: {
          data: products.map(item => item.name)
        },
        series: [{
          data: products.map(item => item.sales)
        }]
      })
    }
    
    // 更新销售趋势
    if (statsRes.data.sales_trend) {
      salesTrendChartInstance.setOption({
        series: [{
          data: statsRes.data.sales_trend
        }]
      })
    }
    
    // 更新漏斗数据
    if (statsRes.data.funnel_data) {
      funnelChartInstance.setOption({
        series: [{
          data: statsRes.data.funnel_data.map(item => ({
            value: item.value,
            name: item.name,
            itemStyle: {
              color: item.color
            }
          }))
        }]
      })
    }
    
    // 更新用户活跃度
    if (statsRes.data.active_users) {
      activeUsersChartInstance.setOption({
        series: [{
          data: statsRes.data.active_users
        }]
      })
    }
    
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

// 初始化所有图表
const initCharts = () => {
  initHotProductsChart()
  initMapChart()
  initSalesTrendChart()
  initFunnelChart()
  initActiveUsersChart()
}

// 响应式处理
const handleResize = () => {
  hotProductsChartInstance?.resize()
  mapChartInstance?.resize()
  salesTrendChartInstance?.resize()
  funnelChartInstance?.resize()
  activeUsersChartInstance?.resize()
}

onMounted(() => {
  // 初始化
  updateTime()
  initCharts()
  fetchData()
  
  // 设置定时器
  timer = setInterval(updateTime, 1000)
  dataTimer = setInterval(fetchData, 10000) // 每10秒更新数据
  
  // 监听窗口变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 清理定时器
  if (timer) clearInterval(timer)
  if (dataTimer) clearInterval(dataTimer)
  
  // 移除监听
  window.removeEventListener('resize', handleResize)
  
  // 销毁图表
  hotProductsChartInstance?.dispose()
  mapChartInstance?.dispose()
  salesTrendChartInstance?.dispose()
  funnelChartInstance?.dispose()
  activeUsersChartInstance?.dispose()
})
</script>

<style scoped>
.dashboard-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(to bottom, #0f1922, #1a2332);
  color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dashboard-header {
  height: 80px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  background: rgba(0, 0, 0, 0.3);
}

.header-left .title {
  font-size: 28px;
  font-weight: bold;
  background: linear-gradient(90deg, #00c6fb, #005bea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-left .time {
  font-size: 14px;
  color: #90979c;
  margin-top: 5px;
}

.header-center {
  display: flex;
  gap: 40px;
}

.stat-card {
  text-align: center;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #00d887;
}

.stat-label {
  font-size: 14px;
  color: #90979c;
  margin-top: 5px;
}

.dashboard-content {
  flex: 1;
  display: flex;
  padding: 20px;
  gap: 20px;
  overflow: hidden;
}

.content-left,
.content-right {
  width: 25%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.content-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-box {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(0, 198, 251, 0.3);
  display: flex;
  flex-direction: column;
  flex: 1;
}

.map-box {
  flex: 2;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #00c6fb;
}

.chart-content {
  flex: 1;
  width: 100%;
}

.order-list {
  flex: 1;
  overflow-y: auto;
}

.order-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  margin-bottom: 10px;
  background: rgba(0, 198, 251, 0.1);
  border-radius: 4px;
  font-size: 14px;
}

.order-time {
  color: #90979c;
}

.order-user {
  color: #fff;
}

.order-amount {
  color: #00d887;
  font-weight: bold;
}

/* 滚动条样式 */
.order-list::-webkit-scrollbar {
  width: 6px;
}

.order-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.order-list::-webkit-scrollbar-thumb {
  background: rgba(0, 198, 251, 0.5);
  border-radius: 3px;
}

.order-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 198, 251, 0.8);
}
</style>