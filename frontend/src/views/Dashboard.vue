<template>
  <div class="dashboard-container">
    <el-container>
      <el-header class="dashboard-header">
        <div class="header-left">
          <h2>电商用户行为分析系统</h2>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-dropdown">
              <el-icon><UserFilled /></el-icon>
              {{ userStore.username }}
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      
      <el-main class="dashboard-main">
        <el-card>
          <h3>欢迎回来，{{ userStore.user.real_name || userStore.username }}！</h3>
          <el-divider />
          
          <!-- 实时统计数据 -->
          <el-row :gutter="20">
            <el-col :span="6">
              <el-card shadow="hover" class="stat-card">
                <el-statistic title="今日访问量" :value="stats.todayPV">
                  <template #prefix>
                    <el-icon><View /></el-icon>
                  </template>
                </el-statistic>
                <div class="stat-footer">
                  <span class="stat-trend" :class="stats.pvTrend > 0 ? 'up' : 'down'">
                    {{ stats.pvTrend > 0 ? '↑' : '↓' }} {{ Math.abs(stats.pvTrend) }}%
                  </span>
                  <span class="stat-label">较昨日</span>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover" class="stat-card">
                <el-statistic title="今日订单数" :value="stats.todayOrders">
                  <template #prefix>
                    <el-icon><ShoppingCart /></el-icon>
                  </template>
                </el-statistic>
                <div class="stat-footer">
                  <span class="stat-trend" :class="stats.orderTrend > 0 ? 'up' : 'down'">
                    {{ stats.orderTrend > 0 ? '↑' : '↓' }} {{ Math.abs(stats.orderTrend) }}%
                  </span>
                  <span class="stat-label">较昨日</span>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover" class="stat-card">
                <el-statistic title="今日销售额" :value="stats.todayRevenue" prefix="¥" :precision="2">
                  <template #prefix>
                    <el-icon><Coin /></el-icon>
                  </template>
                </el-statistic>
                <div class="stat-footer">
                  <span class="stat-trend" :class="stats.revenueTrend > 0 ? 'up' : 'down'">
                    {{ stats.revenueTrend > 0 ? '↑' : '↓' }} {{ Math.abs(stats.revenueTrend) }}%
                  </span>
                  <span class="stat-label">较昨日</span>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover" class="stat-card">
                <el-statistic title="在线用户数" :value="stats.onlineUsers">
                  <template #prefix>
                    <el-icon><User /></el-icon>
                  </template>
                </el-statistic>
                <div class="stat-footer">
                  <span class="stat-trend up">实时</span>
                  <span class="stat-label">活跃用户</span>
                </div>
              </el-card>
            </el-col>
          </el-row>
          
          <el-divider />
          
          <div class="feature-section">
            <h4>快速导航</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-card shadow="hover" class="feature-card" @click="navigateTo('/analytics/users')">
                  <el-icon size="40" color="#409EFF"><User /></el-icon>
                  <h5>用户分析</h5>
                  <p>用户行为、画像、留存分析</p>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card shadow="hover" class="feature-card" @click="navigateTo('/analytics/products')">
                  <el-icon size="40" color="#67C23A"><Goods /></el-icon>
                  <h5>商品分析</h5>
                  <p>商品销售、库存、趋势分析</p>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card shadow="hover" class="feature-card" @click="navigateTo('/analytics/funnel')">
                  <el-icon size="40" color="#E6A23C"><DataAnalysis /></el-icon>
                  <h5>漏斗分析</h5>
                  <p>转化漏斗、路径分析</p>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card shadow="hover" class="feature-card" @click="navigateTo('/reports')">
                  <el-icon size="40" color="#F56C6C"><Document /></el-icon>
                  <h5>报表中心</h5>
                  <p>生成报表、导出数据</p>
                </el-card>
              </el-col>
            </el-row>
          </div>
          
          <el-divider />
          
          <!-- 实时趋势图表 -->
          <div class="chart-section">
            <h4>今日趋势</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card>
                  <template #header>
                    <span>访问趋势</span>
                  </template>
                  <div ref="visitChart" style="height: 300px"></div>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card>
                  <template #header>
                    <span>订单趋势</span>
                  </template>
                  <div ref="orderChart" style="height: 300px"></div>
                </el-card>
              </el-col>
            </el-row>
          </div>
          
          <el-divider />
          
          <!-- 热门商品和最新订单 -->
          <el-row :gutter="20" style="margin-top: 20px">
            <el-col :span="12">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>热门商品TOP5</span>
                    <el-button size="small" @click="navigateTo('/products')">查看全部</el-button>
                  </div>
                </template>
                <el-table :data="hotProducts" style="width: 100%">
                  <el-table-column prop="rank" label="排名" width="60">
                    <template #default="scope">
                      <el-tag :type="getRankType(scope.row.rank)" size="small">{{ scope.row.rank }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="商品名称" />
                  <el-table-column prop="sales" label="销量" width="80" />
                  <el-table-column prop="revenue" label="销售额" width="100">
                    <template #default="scope">
                      ¥{{ scope.row.revenue }}
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>最新订单</span>
                    <el-button size="small" @click="navigateTo('/orders')">查看全部</el-button>
                  </div>
                </template>
                <el-table :data="recentOrders" style="width: 100%">
                  <el-table-column prop="order_no" label="订单号" width="150" />
                  <el-table-column prop="username" label="用户" width="100" />
                  <el-table-column prop="amount" label="金额" width="100">
                    <template #default="scope">
                      ¥{{ scope.row.amount }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="80">
                    <template #default="scope">
                      <el-tag :type="getOrderStatusType(scope.row.status)" size="small">
                        {{ getOrderStatusText(scope.row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="created_at" label="时间">
                    <template #default="scope">
                      {{ formatTime(scope.row.created_at) }}
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '../stores/user'
import { UserFilled, ArrowDown, User, View, ShoppingCart, Coin, Goods, DataAnalysis, Document } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import request from '../utils/request'

const router = useRouter()
const userStore = useUserStore()

// 图表引用
const visitChart = ref(null)
const orderChart = ref(null)
let visitChartInstance = null
let orderChartInstance = null

// 统计数据
const stats = reactive({
  todayPV: 0,
  todayOrders: 0,
  todayRevenue: 0,
  onlineUsers: 0,
  pvTrend: 0,
  orderTrend: 0,
  revenueTrend: 0
})

// 热门商品
const hotProducts = ref([])

// 最新订单
const recentOrders = ref([])

// 定时器
let refreshTimer = null

const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatTime = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  const now = new Date()
  const diff = now - date
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (minutes < 1440) return `${Math.floor(minutes / 60)}小时前`
  return formatDate(dateStr)
}

const getRankType = (rank) => {
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

const getOrderStatusType = (status) => {
  const types = {
    pending: 'warning',
    paid: 'success',
    shipped: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getOrderStatusText = (status) => {
  const texts = {
    pending: '待支付',
    paid: '已支付',
    shipped: '已发货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const navigateTo = (path) => {
  router.push(path)
}

const handleCommand = async (command) => {
  if (command === 'profile') {
    router.push('/profile')
  } else if (command === 'logout') {
    try {
      await userStore.logout()
      ElMessage.success('退出登录成功')
      router.push('/login')
    } catch (error) {
      console.error('退出登录失败：', error)
    }
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const res = await request.get('/dashboard/stats')
    Object.assign(stats, res.data)
  } catch (error) {
    console.error('获取统计数据失败', error)
    // 使用模拟数据
    Object.assign(stats, {
      todayPV: 12543,
      todayOrders: 234,
      todayRevenue: 56789.50,
      onlineUsers: 1250,
      pvTrend: 12.5,
      orderTrend: -3.2,
      revenueTrend: 8.7
    })
  }
}

// 获取热门商品
const fetchHotProducts = async () => {
  try {
    const res = await request.get('/dashboard/hot-products')
    hotProducts.value = res.data
  } catch (error) {
    console.error('获取热门商品失败', error)
    // 使用模拟数据
    hotProducts.value = [
      { rank: 1, name: 'iPhone 15 Pro Max', sales: 532, revenue: 4256000 },
      { rank: 2, name: 'MacBook Pro 14寸', sales: 321, revenue: 3852000 },
      { rank: 3, name: 'AirPods Pro 2', sales: 892, revenue: 1784000 },
      { rank: 4, name: 'iPad Air', sales: 256, revenue: 1024000 },
      { rank: 5, name: 'Apple Watch Series 9', sales: 189, revenue: 756000 }
    ]
  }
}

// 获取最新订单
const fetchRecentOrders = async () => {
  try {
    const res = await request.get('/dashboard/recent-orders')
    recentOrders.value = res.data
  } catch (error) {
    console.error('获取最新订单失败', error)
    // 使用模拟数据
    recentOrders.value = [
      { order_no: 'ORD202401150001', username: '张三', amount: 2599, status: 'paid', created_at: new Date() },
      { order_no: 'ORD202401150002', username: '李四', amount: 899, status: 'pending', created_at: new Date(Date.now() - 300000) },
      { order_no: 'ORD202401150003', username: '王五', amount: 3299, status: 'shipped', created_at: new Date(Date.now() - 600000) },
      { order_no: 'ORD202401150004', username: '赵六', amount: 1599, status: 'completed', created_at: new Date(Date.now() - 900000) },
      { order_no: 'ORD202401150005', username: '钱七', amount: 4999, status: 'paid', created_at: new Date(Date.now() - 1200000) }
    ]
  }
}

// 初始化图表
const initCharts = () => {
  // 访问趋势图
  visitChartInstance = echarts.init(visitChart.value)
  const visitOption = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00']
    },
    yAxis: {
      type: 'value',
      name: '访问量'
    },
    series: [{
      name: '页面浏览量',
      type: 'line',
      smooth: true,
      data: [120, 82, 91, 234, 290, 330, 310, 220],
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(64, 158, 255, 0.5)' },
          { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
        ])
      },
      itemStyle: {
        color: '#409EFF'
      }
    }]
  }
  visitChartInstance.setOption(visitOption)
  
  // 订单趋势图
  orderChartInstance = echarts.init(orderChart.value)
  const orderOption = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00']
    },
    yAxis: {
      type: 'value',
      name: '订单数'
    },
    series: [{
      name: '订单数量',
      type: 'bar',
      data: [15, 8, 12, 34, 45, 52, 48, 32],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#67C23A' },
          { offset: 1, color: '#85CE61' }
        ])
      }
    }]
  }
  orderChartInstance.setOption(orderOption)
}

// 刷新数据
const refreshData = () => {
  fetchStats()
  fetchHotProducts()
  fetchRecentOrders()
}

// 监听窗口大小变化
const handleResize = () => {
  visitChartInstance?.resize()
  orderChartInstance?.resize()
}

onMounted(async () => {
  // 初始加载数据
  refreshData()
  
  // 等待DOM更新后初始化图表
  await nextTick()
  initCharts()
  
  // 设置定时刷新（每30秒）
  refreshTimer = setInterval(refreshData, 30000)
  
  // 添加窗口大小监听
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 清理定时器
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  
  // 移除窗口监听
  window.removeEventListener('resize', handleResize)
  
  // 销毁图表实例
  visitChartInstance?.dispose()
  orderChartInstance?.dispose()
})
</script>

<style scoped>
.dashboard-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.dashboard-header {
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0;
  color: #333;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #666;
}

.user-dropdown:hover {
  color: #409eff;
}

.dashboard-main {
  padding: 20px;
}

.feature-section {
  margin-top: 20px;
}

.feature-section h4 {
  margin-bottom: 20px;
  color: #333;
}

.feature-card {
  text-align: center;
  padding: 20px;
  cursor: pointer;
  transition: transform 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card h5 {
  margin: 15px 0 10px;
  color: #333;
}

.feature-card p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.stat-card {
  position: relative;
}

.stat-footer {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.stat-trend {
  font-weight: bold;
}

.stat-trend.up {
  color: #67C23A;
}

.stat-trend.down {
  color: #F56C6C;
}

.stat-label {
  color: #909399;
}

.chart-section {
  margin-top: 20px;
}

.chart-section h4 {
  margin-bottom: 20px;
  color: #333;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>