<template>
  <div class="order-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>订单管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="exportOrders">
              <el-icon><Download /></el-icon> 导出订单
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="订单号">
          <el-input v-model="searchForm.order_no" placeholder="请输入订单号" clearable />
        </el-form-item>
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="待支付" value="pending" />
            <el-option label="已支付" value="paid" />
            <el-option label="已发货" value="shipped" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="下单时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 订单表格 -->
      <el-table :data="orders" v-loading="loading" style="width: 100%">
        <el-table-column type="expand">
          <template #default="props">
            <div class="order-detail">
              <h4>订单商品</h4>
              <el-table :data="props.row.items" style="width: 100%">
                <el-table-column prop="product_name" label="商品名称" />
                <el-table-column prop="unit_price" label="单价" width="100">
                  <template #default="scope">
                    ¥{{ scope.row.unit_price }}
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" label="数量" width="80" />
                <el-table-column prop="total_price" label="小计" width="100">
                  <template #default="scope">
                    ¥{{ scope.row.total_price }}
                  </template>
                </el-table-column>
              </el-table>
              <div class="order-info">
                <el-descriptions :column="3" border>
                  <el-descriptions-item label="收货地址">
                    {{ props.row.shipping_address || '未填写' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="支付方式">
                    {{ getPaymentMethodLabel(props.row.payment_method) }}
                  </el-descriptions-item>
                  <el-descriptions-item label="运费">
                    ¥{{ props.row.shipping_fee || 0 }}
                  </el-descriptions-item>
                  <el-descriptions-item label="优惠金额">
                    ¥{{ props.row.discount_amount || 0 }}
                  </el-descriptions-item>
                  <el-descriptions-item label="创建时间">
                    {{ formatDateTime(props.row.created_at) }}
                  </el-descriptions-item>
                  <el-descriptions-item label="更新时间">
                    {{ formatDateTime(props.row.updated_at) }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="order_no" label="订单号" width="180" />
        <el-table-column prop="user_name" label="用户" width="120" />
        <el-table-column prop="total_amount" label="订单金额" width="120">
          <template #default="scope">
            <span class="price">¥{{ scope.row.total_amount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="order_status" label="订单状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.order_status)">
              {{ getStatusLabel(scope.row.order_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="payment_method" label="支付方式" width="100">
          <template #default="scope">
            {{ getPaymentMethodLabel(scope.row.payment_method) }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="下单时间" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="handleShip(scope.row)"
              v-if="scope.row.order_status === 'paid'"
            >
              发货
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleCancel(scope.row)"
              v-if="scope.row.order_status === 'pending'"
            >
              取消
            </el-button>
            <el-button size="small" @click="handlePrint(scope.row)">打印</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    
    <!-- 发货对话框 -->
    <el-dialog v-model="shipDialogVisible" title="订单发货" width="500px">
      <el-form :model="shipForm" label-width="100px">
        <el-form-item label="物流公司">
          <el-select v-model="shipForm.carrier" placeholder="请选择物流公司" style="width: 100%">
            <el-option label="顺丰速运" value="sf" />
            <el-option label="圆通快递" value="yt" />
            <el-option label="中通快递" value="zt" />
            <el-option label="申通快递" value="st" />
            <el-option label="韵达快递" value="yd" />
            <el-option label="京东快递" value="jd" />
          </el-select>
        </el-form-item>
        <el-form-item label="物流单号">
          <el-input v-model="shipForm.tracking_no" placeholder="请输入物流单号" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="shipForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="shipDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmShip">确认发货</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import request from '../../utils/request'

const orders = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const dateRange = ref([])

const searchForm = reactive({
  order_no: '',
  username: '',
  status: '',
  start_date: '',
  end_date: ''
})

const shipDialogVisible = ref(false)
const currentOrder = ref(null)
const shipForm = reactive({
  carrier: '',
  tracking_no: '',
  remark: ''
})

// 获取订单列表
const fetchOrders = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...searchForm
    }
    const res = await request.get('/orders', { params })
    orders.value = res.data.orders || []
    total.value = res.data.pagination?.total_count || 0
  } catch (error) {
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 处理日期变化
const handleDateChange = (value) => {
  if (value) {
    searchForm.start_date = value[0]
    searchForm.end_date = value[1]
  } else {
    searchForm.start_date = ''
    searchForm.end_date = ''
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchOrders()
}

// 重置搜索
const resetSearch = () => {
  searchForm.order_no = ''
  searchForm.username = ''
  searchForm.status = ''
  searchForm.start_date = ''
  searchForm.end_date = ''
  dateRange.value = []
  handleSearch()
}

// 分页
const handleSizeChange = () => {
  currentPage.value = 1
  fetchOrders()
}

const handleCurrentChange = () => {
  fetchOrders()
}

// 查看订单
const handleView = (row) => {
  ElMessage.info('订单详情功能开发中')
}

// 发货
const handleShip = (row) => {
  currentOrder.value = row
  shipForm.carrier = ''
  shipForm.tracking_no = ''
  shipForm.remark = ''
  shipDialogVisible.value = true
}

// 确认发货
const confirmShip = async () => {
  if (!shipForm.carrier || !shipForm.tracking_no) {
    ElMessage.warning('请填写物流信息')
    return
  }
  
  try {
    await request.put(`/orders/${currentOrder.value.id}/ship`, shipForm)
    ElMessage.success('发货成功')
    shipDialogVisible.value = false
    fetchOrders()
  } catch (error) {
    ElMessage.error('发货失败')
  }
}

// 取消订单
const handleCancel = async (row) => {
  try {
    await ElMessageBox.confirm('确定要取消该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await request.put(`/orders/${row.id}/cancel`)
    ElMessage.success('订单已取消')
    fetchOrders()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消订单失败')
    }
  }
}

// 打印订单
const handlePrint = (row) => {
  ElMessage.info('打印功能开发中')
}

// 导出订单
const exportOrders = async () => {
  try {
    const params = { ...searchForm }
    const res = await request.get('/orders/export', { 
      params,
      responseType: 'blob'
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([res.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `orders_${new Date().getTime()}.xlsx`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    'pending': 'warning',
    'paid': 'primary',
    'shipped': 'info',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return types[status] || 'info'
}

// 获取状态标签
const getStatusLabel = (status) => {
  const labels = {
    'pending': '待支付',
    'paid': '已支付',
    'shipped': '已发货',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return labels[status] || status
}

// 获取支付方式标签
const getPaymentMethodLabel = (method) => {
  const labels = {
    'alipay': '支付宝',
    'wechat': '微信支付',
    'credit_card': '信用卡'
  }
  return labels[method] || '未支付'
}

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

onMounted(() => {
  fetchOrders()
})
</script>

<style scoped>
.order-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.price {
  color: #f56c6c;
  font-weight: bold;
}

.order-detail {
  padding: 20px;
}

.order-detail h4 {
  margin-bottom: 15px;
  color: #303133;
}

.order-info {
  margin-top: 20px;
}

.el-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>