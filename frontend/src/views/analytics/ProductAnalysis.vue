<template>
  <div class="product-analysis">
    <el-row :gutter="20">
      <!-- 筛选条件 -->
      <el-col :span="24">
        <el-card>
          <el-form :inline="true" :model="filterForm">
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleDateChange"
              />
            </el-form-item>
            <el-form-item label="商品分类">
              <el-select v-model="filterForm.category_id" placeholder="全部分类" clearable>
                <el-option
                  v-for="category in categories"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="fetchData">查询</el-button>
              <el-button @click="resetFilter">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <!-- 关键指标 -->
      <el-col :span="6">
        <el-card>
          <el-statistic title="商品总数" :value="stats.totalProducts">
            <template #prefix>
              <el-icon><Goods /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-statistic title="总销售额" :value="stats.totalRevenue" prefix="¥">
            <template #suffix>
              <span style="font-size: 14px">元</span>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-statistic title="总销量" :value="stats.totalSales">
            <template #suffix>
              <span style="font-size: 14px">件</span>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-statistic title="平均客单价" :value="stats.avgOrderValue" :precision="2" prefix="¥">
            <template #suffix>
              <span style="font-size: 14px">元</span>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      
      <!-- 销售趋势图 -->
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="chart-header">
              <span>销售趋势</span>
              <el-radio-group v-model="trendType" size="small" @change="updateSalesTrend">
                <el-radio-button label="day">按日</el-radio-button>
                <el-radio-button label="week">按周</el-radio-button>
                <el-radio-button label="month">按月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="salesTrendChart" style="height: 400px"></div>
        </el-card>
      </el-col>
      
      <!-- 分类销售分布 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>分类销售分布</span>
          </template>
          <div ref="categoryChart" style="height: 400px"></div>
        </el-card>
      </el-col>
      
      <!-- 商品销量排行 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>商品销量TOP10</span>
          </template>
          <div ref="topProductsChart" style="height: 400px"></div>
        </el-card>
      </el-col>
      
      <!-- 价格区间分析 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>价格区间分析</span>
          </template>
          <div ref="priceRangeChart" style="height: 400px"></div>
        </el-card>
      </el-col>
      
      <!-- 库存周转率 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>库存周转率</span>
          </template>
          <div ref="inventoryTurnoverChart" style="height: 400px"></div>
        </el-card>
      </el-col>
      
      <!-- 商品详细数据表格 -->
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="table-header">
              <span>商品销售明细</span>
              <el-button type="primary" size="small" @click="exportData">
                <el-icon><Download /></el-icon> 导出数据
              </el-button>
            </div>
          </template>
          <el-table :data="productList" style="width: 100%" v-loading="loading">
            <el-table-column prop="product_name" label="商品名称" min-width="150" />
            <el-table-column prop="category_name" label="分类" width="120" />
            <el-table-column prop="price" label="单价" width="100">
              <template #default="scope">
                ¥{{ scope.row.price }}
              </template>
            </el-table-column>
            <el-table-column prop="sales_quantity" label="销量" width="100" sortable>
              <template #default="scope">
                <el-tag type="primary">{{ scope.row.sales_quantity }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="sales_amount" label="销售额" width="120" sortable>
              <template #default="scope">
                ¥{{ scope.row.sales_amount }}
              </template>
            </el-table-column>
            <el-table-column prop="view_count" label="浏览量" width="100" sortable />
            <el-table-column prop="conversion_rate" label="转化率" width="100" sortable>
              <template #default="scope">
                {{ (scope.row.conversion_rate * 100).toFixed(2) }}%
              </template>
            </el-table-column>
            <el-table-column prop="stock" label="库存" width="80">
              <template #default="scope">
                <el-tag :type="getStockType(scope.row.stock)">{{ scope.row.stock }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="turnover_rate" label="周转率" width="100">
              <template #default="scope">
                {{ scope.row.turnover_rate?.toFixed(2) || '-' }}
              </template>
            </el-table-column>
          </el-table>
          
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="fetchProductList"
            @current-change="fetchProductList"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import request from '../../utils/request'
import { Goods, Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 数据
const dateRange = ref([])
const filterForm = reactive({
  category_id: '',
  start_date: '',
  end_date: ''
})

const stats = reactive({
  totalProducts: 0,
  totalRevenue: 0,
  totalSales: 0,
  avgOrderValue: 0
})

const categories = ref([])
const productList = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const trendType = ref('day')

// 图表实例
let salesTrendChartInstance = null
let categoryChartInstance = null
let topProductsChartInstance = null
let priceRangeChartInstance = null
let inventoryTurnoverChartInstance = null

// 图表ref
const salesTrendChart = ref(null)
const categoryChart = ref(null)
const topProductsChart = ref(null)
const priceRangeChart = ref(null)
const inventoryTurnoverChart = ref(null)

// 处理日期变化
const handleDateChange = (value) => {
  if (value) {
    filterForm.start_date = value[0]
    filterForm.end_date = value[1]
  } else {
    filterForm.start_date = ''
    filterForm.end_date = ''
  }
}

// 重置筛选
const resetFilter = () => {
  dateRange.value = []
  filterForm.category_id = ''
  filterForm.start_date = ''
  filterForm.end_date = ''
  fetchData()
}

// 获取数据
const fetchData = async () => {
  try {
    // 获取统计数据
    const statsRes = await request.get('/analytics/product-stats', { params: filterForm })
    Object.assign(stats, statsRes.data)
    
    // 获取销售趋势
    updateSalesTrend()
    
    // 获取分类销售分布
    const categoryRes = await request.get('/analytics/category-sales', { params: filterForm })
    updateCategoryChart(categoryRes.data)
    
    // 获取商品销量排行
    const topRes = await request.get('/analytics/top-products', { params: filterForm })
    updateTopProductsChart(topRes.data)
    
    // 获取价格区间分析
    const priceRes = await request.get('/analytics/price-range', { params: filterForm })
    updatePriceRangeChart(priceRes.data)
    
    // 获取库存周转率
    const turnoverRes = await request.get('/analytics/inventory-turnover', { params: filterForm })
    updateInventoryTurnoverChart(turnoverRes.data)
    
    // 获取商品列表
    fetchProductList()
    
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

// 获取商品列表
const fetchProductList = async () => {
  loading.value = true
  try {
    const params = {
      ...filterForm,
      page: currentPage.value,
      limit: pageSize.value
    }
    const res = await request.get('/analytics/product-list', { params })
    productList.value = res.data.products || []
    total.value = res.data.total || 0
  } catch (error) {
    console.error('获取商品列表失败', error)
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const res = await request.get('/categories')
    categories.value = res.data || []
  } catch (error) {
    console.error('获取分类失败', error)
  }
}

// 初始化图表
const initCharts = () => {
  // 销售趋势图
  salesTrendChartInstance = echarts.init(salesTrendChart.value)
  const salesTrendOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['销售额', '销量']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: []
    },
    yAxis: [
      {
        type: 'value',
        name: '销售额',
        axisLabel: {
          formatter: '¥{value}'
        }
      },
      {
        type: 'value',
        name: '销量',
        axisLabel: {
          formatter: '{value}件'
        }
      }
    ],
    series: [
      {
        name: '销售额',
        type: 'line',
        smooth: true,
        data: [],
        itemStyle: {
          color: '#5470c6'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(84, 112, 198, 0.5)' },
            { offset: 1, color: 'rgba(84, 112, 198, 0.1)' }
          ])
        }
      },
      {
        name: '销量',
        type: 'line',
        smooth: true,
        yAxisIndex: 1,
        data: [],
        itemStyle: {
          color: '#91cc75'
        }
      }
    ]
  }
  salesTrendChartInstance.setOption(salesTrendOption)
  
  // 分类销售分布图
  categoryChartInstance = echarts.init(categoryChart.value)
  const categoryOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '销售额',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: []
      }
    ]
  }
  categoryChartInstance.setOption(categoryOption)
  
  // 商品销量排行图
  topProductsChartInstance = echarts.init(topProductsChart.value)
  const topProductsOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: []
    },
    series: [
      {
        name: '销量',
        type: 'bar',
        data: [],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }
  topProductsChartInstance.setOption(topProductsOption)
  
  // 价格区间分析图
  priceRangeChartInstance = echarts.init(priceRangeChart.value)
  const priceRangeOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0-50', '50-100', '100-200', '200-500', '500+']
    },
    yAxis: {
      type: 'value',
      name: '商品数量'
    },
    series: [
      {
        name: '商品数量',
        type: 'bar',
        data: [],
        itemStyle: {
          color: '#fac858'
        }
      },
      {
        name: '销售额占比',
        type: 'line',
        data: [],
        itemStyle: {
          color: '#ee6666'
        }
      }
    ]
  }
  priceRangeChartInstance.setOption(priceRangeOption)
  
  // 库存周转率图
  inventoryTurnoverChartInstance = echarts.init(inventoryTurnoverChart.value)
  const inventoryTurnoverOption = {
    tooltip: {
      trigger: 'axis'
    },
    radar: {
      indicator: []
    },
    series: [{
      name: '库存周转率',
      type: 'radar',
      data: []
    }]
  }
  inventoryTurnoverChartInstance.setOption(inventoryTurnoverOption)
}

// 更新销售趋势
const updateSalesTrend = async () => {
  try {
    const params = {
      ...filterForm,
      trend_type: trendType.value
    }
    const res = await request.get('/analytics/sales-trend', { params })
    const data = res.data || []
    
    salesTrendChartInstance.setOption({
      xAxis: {
        data: data.map(item => item.date)
      },
      series: [
        {
          name: '销售额',
          data: data.map(item => item.revenue)
        },
        {
          name: '销量',
          data: data.map(item => item.sales)
        }
      ]
    })
  } catch (error) {
    console.error('获取销售趋势失败', error)
  }
}

// 更新分类销售分布
const updateCategoryChart = (data) => {
  categoryChartInstance.setOption({
    series: [{
      data: data.map(item => ({
        value: item.sales_amount,
        name: item.category_name
      }))
    }]
  })
}

// 更新商品销量排行
const updateTopProductsChart = (data) => {
  const top10 = data.slice(0, 10).reverse()
  topProductsChartInstance.setOption({
    yAxis: {
      data: top10.map(item => item.product_name)
    },
    series: [{
      data: top10.map(item => item.sales_quantity)
    }]
  })
}

// 更新价格区间分析
const updatePriceRangeChart = (data) => {
  priceRangeChartInstance.setOption({
    series: [
      {
        name: '商品数量',
        data: data.map(item => item.product_count)
      },
      {
        name: '销售额占比',
        data: data.map(item => item.revenue_ratio * 100)
      }
    ]
  })
}

// 更新库存周转率
const updateInventoryTurnoverChart = (data) => {
  inventoryTurnoverChartInstance.setOption({
    radar: {
      indicator: data.map(item => ({
        name: item.category_name,
        max: Math.max(...data.map(d => d.turnover_rate))
      }))
    },
    series: [{
      data: [{
        value: data.map(item => item.turnover_rate),
        name: '库存周转率'
      }]
    }]
  })
}

// 获取库存状态类型
const getStockType = (stock) => {
  if (stock > 100) return 'success'
  if (stock > 20) return 'warning'
  return 'danger'
}

// 导出数据
const exportData = async () => {
  try {
    const params = {
      ...filterForm,
      export: true
    }
    const res = await request.get('/analytics/product-export', { 
      params,
      responseType: 'blob'
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([res.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `product_analysis_${new Date().getTime()}.xlsx`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 监听窗口大小变化
const handleResize = () => {
  salesTrendChartInstance?.resize()
  categoryChartInstance?.resize()
  topProductsChartInstance?.resize()
  priceRangeChartInstance?.resize()
  inventoryTurnoverChartInstance?.resize()
}

onMounted(async () => {
  await nextTick()
  initCharts()
  fetchCategories()
  fetchData()
  
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  
  // 销毁图表实例
  salesTrendChartInstance?.dispose()
  categoryChartInstance?.dispose()
  topProductsChartInstance?.dispose()
  priceRangeChartInstance?.dispose()
  inventoryTurnoverChartInstance?.dispose()
})
</script>

<style scoped>
.product-analysis {
  padding: 20px;
}

.el-row {
  margin-bottom: 20px;
}

.el-col {
  margin-bottom: 20px;
}

.chart-header,
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>