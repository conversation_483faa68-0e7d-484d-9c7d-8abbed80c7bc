<template>
  <div class="funnel-analysis">
    <el-row :gutter="20">
      <!-- 筛选条件 -->
      <el-col :span="24">
        <el-card>
          <el-form :inline="true" :model="filterForm">
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleDateChange"
              />
            </el-form-item>
            <el-form-item label="漏斗类型">
              <el-select v-model="filterForm.funnel_type" placeholder="请选择" @change="fetchData">
                <el-option label="购买转化漏斗" value="purchase" />
                <el-option label="注册转化漏斗" value="register" />
                <el-option label="搜索转化漏斗" value="search" />
                <el-option label="自定义漏斗" value="custom" />
              </el-select>
            </el-form-item>
            <el-form-item label="用户分组">
              <el-select v-model="filterForm.user_group" placeholder="全部用户" clearable>
                <el-option label="新用户" value="new" />
                <el-option label="老用户" value="old" />
                <el-option label="VIP用户" value="vip" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="fetchData">查询</el-button>
              <el-button @click="resetFilter">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <!-- 漏斗主图 -->
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="chart-header">
              <span>转化漏斗</span>
              <div>
                <el-button size="small" @click="showConfigDialog">配置漏斗</el-button>
                <el-button size="small" type="primary" @click="saveAsTemplate">保存为模板</el-button>
              </div>
            </div>
          </template>
          <div ref="funnelChart" style="height: 500px"></div>
        </el-card>
      </el-col>
      
      <!-- 转化率统计 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>转化率统计</span>
          </template>
          <div class="conversion-stats">
            <div v-for="(step, index) in conversionSteps" :key="index" class="conversion-item">
              <div class="step-info">
                <span class="step-name">{{ step.name }}</span>
                <el-tag :type="getConversionType(step.rate)" size="small">
                  {{ (step.rate * 100).toFixed(2) }}%
                </el-tag>
              </div>
              <el-progress 
                :percentage="step.rate * 100" 
                :color="getProgressColor(step.rate)"
                :stroke-width="10"
              />
              <div class="step-detail">
                <span>用户数: {{ step.users }}</span>
                <span v-if="index > 0" class="loss-rate">
                  流失: {{ ((1 - step.rate) * 100).toFixed(2) }}%
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 转化趋势 -->
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="chart-header">
              <span>转化趋势</span>
              <el-radio-group v-model="trendType" size="small" @change="updateTrend">
                <el-radio-button label="day">按日</el-radio-button>
                <el-radio-button label="week">按周</el-radio-button>
                <el-radio-button label="month">按月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="trendChart" style="height: 400px"></div>
        </el-card>
      </el-col>
      
      <!-- 用户路径分析 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户路径分析</span>
          </template>
          <div ref="pathChart" style="height: 400px"></div>
        </el-card>
      </el-col>
      
      <!-- 流失原因分析 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>流失原因分析</span>
          </template>
          <div class="loss-analysis">
            <el-table :data="lossReasons" style="width: 100%">
              <el-table-column prop="step" label="流失环节" width="150" />
              <el-table-column prop="reason" label="流失原因" min-width="200" />
              <el-table-column prop="count" label="用户数" width="100">
                <template #default="scope">
                  <el-tag>{{ scope.row.count }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="percentage" label="占比" width="100">
                <template #default="scope">
                  {{ scope.row.percentage }}%
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button size="small" @click="viewLossDetail(scope.row)">详情</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
      
      <!-- 对比分析 -->
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="chart-header">
              <span>对比分析</span>
              <el-button size="small" @click="showCompareDialog">添加对比</el-button>
            </div>
          </template>
          <div ref="compareChart" style="height: 400px"></div>
        </el-card>
      </el-col>
      
      <!-- 优化建议 -->
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>优化建议</span>
          </template>
          <div class="suggestions">
            <el-alert
              v-for="(suggestion, index) in suggestions"
              :key="index"
              :title="suggestion.title"
              :type="suggestion.type"
              :description="suggestion.description"
              show-icon
              :closable="false"
              style="margin-bottom: 10px"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 配置漏斗对话框 -->
    <el-dialog v-model="configDialogVisible" title="配置漏斗" width="600px">
      <el-form :model="funnelConfig" label-width="100px">
        <el-form-item label="漏斗名称">
          <el-input v-model="funnelConfig.name" placeholder="请输入漏斗名称" />
        </el-form-item>
        <el-form-item label="漏斗步骤">
          <div v-for="(step, index) in funnelConfig.steps" :key="index" class="step-config">
            <el-input v-model="step.name" placeholder="步骤名称" style="width: 150px" />
            <el-select v-model="step.event" placeholder="选择事件" style="width: 150px; margin: 0 10px">
              <el-option label="页面浏览" value="view" />
              <el-option label="商品点击" value="click" />
              <el-option label="加入购物车" value="add_cart" />
              <el-option label="提交订单" value="submit_order" />
              <el-option label="支付成功" value="pay_success" />
            </el-select>
            <el-button v-if="index > 0" type="danger" icon="Delete" circle @click="removeStep(index)" />
          </div>
          <el-button type="primary" @click="addStep" style="margin-top: 10px">添加步骤</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="applyConfig">应用配置</el-button>
      </template>
    </el-dialog>
    
    <!-- 对比分析对话框 -->
    <el-dialog v-model="compareDialogVisible" title="添加对比" width="500px">
      <el-form :model="compareForm" label-width="100px">
        <el-form-item label="对比维度">
          <el-select v-model="compareForm.dimension" placeholder="请选择">
            <el-option label="时间对比" value="time" />
            <el-option label="用户群体对比" value="user_group" />
            <el-option label="渠道对比" value="channel" />
            <el-option label="设备对比" value="device" />
          </el-select>
        </el-form-item>
        <el-form-item label="对比项" v-if="compareForm.dimension">
          <el-select v-model="compareForm.items" multiple placeholder="请选择对比项">
            <el-option v-for="item in compareOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="compareDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmCompare">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import request from '../../utils/request'
import { ElMessage } from 'element-plus'

// 数据
const dateRange = ref([])
const filterForm = reactive({
  funnel_type: 'purchase',
  user_group: '',
  start_date: '',
  end_date: ''
})

const conversionSteps = ref([])
const lossReasons = ref([])
const suggestions = ref([])
const trendType = ref('day')

// 对话框
const configDialogVisible = ref(false)
const compareDialogVisible = ref(false)

const funnelConfig = reactive({
  name: '',
  steps: [
    { name: '浏览商品', event: 'view' },
    { name: '加入购物车', event: 'add_cart' },
    { name: '提交订单', event: 'submit_order' },
    { name: '支付成功', event: 'pay_success' }
  ]
})

const compareForm = reactive({
  dimension: '',
  items: []
})

// 图表实例
let funnelChartInstance = null
let trendChartInstance = null
let pathChartInstance = null
let compareChartInstance = null

// 图表ref
const funnelChart = ref(null)
const trendChart = ref(null)
const pathChart = ref(null)
const compareChart = ref(null)

// 计算属性
const compareOptions = computed(() => {
  const options = {
    time: [
      { label: '本周', value: 'this_week' },
      { label: '上周', value: 'last_week' },
      { label: '本月', value: 'this_month' },
      { label: '上月', value: 'last_month' }
    ],
    user_group: [
      { label: '新用户', value: 'new' },
      { label: '老用户', value: 'old' },
      { label: 'VIP用户', value: 'vip' },
      { label: '普通用户', value: 'normal' }
    ],
    channel: [
      { label: '直接访问', value: 'direct' },
      { label: '搜索引擎', value: 'search' },
      { label: '社交媒体', value: 'social' },
      { label: '广告投放', value: 'ads' }
    ],
    device: [
      { label: '桌面端', value: 'desktop' },
      { label: '移动端', value: 'mobile' },
      { label: '平板', value: 'tablet' }
    ]
  }
  return options[compareForm.dimension] || []
})

// 处理日期变化
const handleDateChange = (value) => {
  if (value) {
    filterForm.start_date = value[0]
    filterForm.end_date = value[1]
  } else {
    filterForm.start_date = ''
    filterForm.end_date = ''
  }
}

// 重置筛选
const resetFilter = () => {
  dateRange.value = []
  filterForm.funnel_type = 'purchase'
  filterForm.user_group = ''
  filterForm.start_date = ''
  filterForm.end_date = ''
  fetchData()
}

// 获取数据
const fetchData = async () => {
  try {
    // 获取漏斗数据
    const funnelRes = await request.get('/analytics/funnel-data', { params: filterForm })
    updateFunnelChart(funnelRes.data)
    conversionSteps.value = funnelRes.data.steps || []
    
    // 获取转化趋势
    updateTrend()
    
    // 获取用户路径
    const pathRes = await request.get('/analytics/user-path', { params: filterForm })
    updatePathChart(pathRes.data)
    
    // 获取流失原因
    const lossRes = await request.get('/analytics/loss-reasons', { params: filterForm })
    lossReasons.value = lossRes.data || []
    
    // 获取优化建议
    const suggestRes = await request.get('/analytics/funnel-suggestions', { params: filterForm })
    suggestions.value = suggestRes.data || []
    
    // 更新对比分析
    updateCompareChart()
    
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

// 初始化图表
const initCharts = () => {
  // 漏斗图
  funnelChartInstance = echarts.init(funnelChart.value)
  const funnelOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)'
    },
    toolbox: {
      feature: {
        dataView: { readOnly: false },
        saveAsImage: {}
      }
    },
    series: [
      {
        name: '转化漏斗',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: []
      }
    ]
  }
  funnelChartInstance.setOption(funnelOption)
  
  // 趋势图
  trendChartInstance = echarts.init(trendChart.value)
  const trendOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: []
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: []
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: []
  }
  trendChartInstance.setOption(trendOption)
  
  // 路径分析图
  pathChartInstance = echarts.init(pathChart.value)
  const pathOption = {
    tooltip: {
      trigger: 'item'
    },
    series: {
      type: 'sankey',
      layout: 'none',
      emphasis: {
        focus: 'adjacency'
      },
      data: [],
      links: []
    }
  }
  pathChartInstance.setOption(pathOption)
  
  // 对比分析图
  compareChartInstance = echarts.init(compareChart.value)
  const compareOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: []
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: []
  }
  compareChartInstance.setOption(compareOption)
}

// 更新漏斗图
const updateFunnelChart = (data) => {
  const funnelData = data.steps?.map((step, index) => ({
    value: step.rate * 100,
    name: `${step.name}\n${step.users}人`
  })) || []
  
  funnelChartInstance.setOption({
    series: [{
      data: funnelData
    }]
  })
}

// 更新趋势图
const updateTrend = async () => {
  try {
    const params = {
      ...filterForm,
      trend_type: trendType.value
    }
    const res = await request.get('/analytics/funnel-trend', { params })
    const data = res.data || {}
    
    const series = Object.keys(data.series || {}).map(key => ({
      name: key,
      type: 'line',
      smooth: true,
      data: data.series[key]
    }))
    
    trendChartInstance.setOption({
      legend: {
        data: Object.keys(data.series || {})
      },
      xAxis: {
        data: data.dates || []
      },
      series
    })
  } catch (error) {
    console.error('获取趋势数据失败', error)
  }
}

// 更新路径分析图
const updatePathChart = (data) => {
  pathChartInstance.setOption({
    series: {
      data: data.nodes || [],
      links: data.links || []
    }
  })
}

// 更新对比分析图
const updateCompareChart = async () => {
  if (!compareForm.dimension || compareForm.items.length === 0) {
    // 显示默认对比数据
    compareChartInstance.setOption({
      xAxis: {
        data: ['浏览', '加购', '下单', '支付']
      },
      series: [{
        name: '当前',
        type: 'bar',
        data: [100, 60, 40, 20]
      }]
    })
    return
  }
  
  try {
    const res = await request.get('/analytics/funnel-compare', { 
      params: {
        ...filterForm,
        dimension: compareForm.dimension,
        items: compareForm.items
      }
    })
    const data = res.data || {}
    
    const series = compareForm.items.map(item => ({
      name: compareOptions.value.find(opt => opt.value === item)?.label || item,
      type: 'bar',
      data: data[item] || []
    }))
    
    compareChartInstance.setOption({
      legend: {
        data: series.map(s => s.name)
      },
      xAxis: {
        data: data.steps || []
      },
      series
    })
  } catch (error) {
    console.error('获取对比数据失败', error)
  }
}

// 获取转化率类型
const getConversionType = (rate) => {
  if (rate > 0.5) return 'success'
  if (rate > 0.3) return 'warning'
  return 'danger'
}

// 获取进度条颜色
const getProgressColor = (rate) => {
  if (rate > 0.5) return '#67c23a'
  if (rate > 0.3) return '#e6a23c'
  return '#f56c6c'
}

// 显示配置对话框
const showConfigDialog = () => {
  configDialogVisible.value = true
}

// 添加步骤
const addStep = () => {
  funnelConfig.steps.push({ name: '', event: '' })
}

// 移除步骤
const removeStep = (index) => {
  funnelConfig.steps.splice(index, 1)
}

// 应用配置
const applyConfig = () => {
  // 这里应该发送配置到后端
  ElMessage.success('配置已应用')
  configDialogVisible.value = false
  fetchData()
}

// 保存为模板
const saveAsTemplate = async () => {
  try {
    await request.post('/analytics/funnel-template', {
      name: funnelConfig.name || '自定义漏斗',
      config: funnelConfig
    })
    ElMessage.success('模板保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 显示对比对话框
const showCompareDialog = () => {
  compareDialogVisible.value = true
}

// 确认对比
const confirmCompare = () => {
  compareDialogVisible.value = false
  updateCompareChart()
}

// 查看流失详情
const viewLossDetail = (row) => {
  ElMessage.info(`查看 ${row.step} 的流失详情`)
}

// 监听窗口大小变化
const handleResize = () => {
  funnelChartInstance?.resize()
  trendChartInstance?.resize()
  pathChartInstance?.resize()
  compareChartInstance?.resize()
}

onMounted(async () => {
  await nextTick()
  initCharts()
  fetchData()
  
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  
  // 销毁图表实例
  funnelChartInstance?.dispose()
  trendChartInstance?.dispose()
  pathChartInstance?.dispose()
  compareChartInstance?.dispose()
})
</script>

<style scoped>
.funnel-analysis {
  padding: 20px;
}

.el-row {
  margin-bottom: 20px;
}

.el-col {
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conversion-stats {
  padding: 10px;
}

.conversion-item {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.step-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.step-name {
  font-weight: bold;
  font-size: 14px;
}

.step-detail {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-size: 12px;
  color: #666;
}

.loss-rate {
  color: #f56c6c;
}

.loss-analysis {
  padding: 10px;
}

.suggestions {
  padding: 10px;
}

.step-config {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>