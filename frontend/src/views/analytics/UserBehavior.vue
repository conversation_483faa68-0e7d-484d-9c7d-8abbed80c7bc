<template>
  <div class="user-behavior">
    <el-row :gutter="20">
      <!-- 筛选条件 -->
      <el-col :span="24">
        <el-card>
          <el-form :inline="true" :model="filterForm">
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleDateChange"
              />
            </el-form-item>
            <el-form-item label="用户ID">
              <el-input v-model="filterForm.user_id" placeholder="请输入用户ID" clearable />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="fetchData">查询</el-button>
              <el-button @click="resetFilter">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <!-- 统计卡片 -->
      <el-col :span="6">
        <el-card>
          <el-statistic title="今日活跃用户" :value="stats.activeUsers">
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-statistic title="今日页面访问量" :value="stats.pageViews">
            <template #prefix>
              <el-icon><View /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-statistic title="平均停留时长" :value="stats.avgDuration" suffix="秒">
            <template #prefix>
              <el-icon><Timer /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-statistic title="跳出率" :value="stats.bounceRate" suffix="%">
            <template #prefix>
              <el-icon><TrendCharts /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      
      <!-- 行为类型分布 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户行为分布</span>
          </template>
          <div ref="behaviorChart" style="height: 400px"></div>
        </el-card>
      </el-col>
      
      <!-- 设备类型分布 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>设备类型分布</span>
          </template>
          <div ref="deviceChart" style="height: 400px"></div>
        </el-card>
      </el-col>
      
      <!-- 时间分布 -->
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>24小时行为趋势</span>
          </template>
          <div ref="timeChart" style="height: 400px"></div>
        </el-card>
      </el-col>
      
      <!-- 热门页面 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>热门页面TOP10</span>
          </template>
          <el-table :data="topPages" style="width: 100%">
            <el-table-column type="index" label="排名" width="60" />
            <el-table-column prop="page_url" label="页面URL" />
            <el-table-column prop="views" label="访问量" width="100">
              <template #default="scope">
                <el-tag type="primary">{{ scope.row.views }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <!-- 热门商品 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>热门商品TOP10</span>
          </template>
          <el-table :data="topProducts" style="width: 100%">
            <el-table-column type="index" label="排名" width="60" />
            <el-table-column prop="product_name" label="商品名称" />
            <el-table-column prop="clicks" label="点击量" width="100">
              <template #default="scope">
                <el-tag type="success">{{ scope.row.clicks }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import request from '../../utils/request'
import { User, View, Timer, TrendCharts } from '@element-plus/icons-vue'

const dateRange = ref([])
const filterForm = reactive({
  user_id: '',
  start_date: '',
  end_date: ''
})

const stats = reactive({
  activeUsers: 0,
  pageViews: 0,
  avgDuration: 0,
  bounceRate: 0
})

const topPages = ref([])
const topProducts = ref([])

let behaviorChartInstance = null
let deviceChartInstance = null
let timeChartInstance = null

const behaviorChart = ref(null)
const deviceChart = ref(null)
const timeChart = ref(null)

const handleDateChange = (value) => {
  if (value) {
    filterForm.start_date = value[0]
    filterForm.end_date = value[1]
  } else {
    filterForm.start_date = ''
    filterForm.end_date = ''
  }
}

const resetFilter = () => {
  dateRange.value = []
  filterForm.user_id = ''
  filterForm.start_date = ''
  filterForm.end_date = ''
  fetchData()
}

const fetchData = async () => {
  try {
    // 获取实时统计数据
    const realtimeRes = await request.get('/analytics/realtime')
    stats.activeUsers = realtimeRes.data.online_users
    stats.pageViews = realtimeRes.data.page_views_today
    topPages.value = realtimeRes.data.top_pages || []
    topProducts.value = realtimeRes.data.top_products || []
    
    // 获取行为分析数据
    const behaviorRes = await request.get('/analytics/behavior-analysis', {
      params: filterForm
    })
    
    // 更新图表
    updateBehaviorChart(behaviorRes.data.behavior_distribution || [])
    updateDeviceChart(behaviorRes.data.device_distribution || [])
    updateTimeChart(behaviorRes.data.time_distribution || [])
    
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

const initCharts = () => {
  // 初始化行为分布图
  behaviorChartInstance = echarts.init(behaviorChart.value)
  const behaviorOption = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      bottom: '0%'
    },
    series: [
      {
        name: '行为类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: []
      }
    ]
  }
  behaviorChartInstance.setOption(behaviorOption)
  
  // 初始化设备分布图
  deviceChartInstance = echarts.init(deviceChart.value)
  const deviceOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: []
    },
    series: [
      {
        name: '设备数量',
        type: 'bar',
        data: [],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }
  deviceChartInstance.setOption(deviceOption)
  
  // 初始化时间分布图
  timeChartInstance = echarts.init(timeChart.value)
  const timeOption = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: Array.from({ length: 24 }, (_, i) => `${i}:00`)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '访问量',
        type: 'line',
        smooth: true,
        data: [],
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(58,77,233,0.8)' },
            { offset: 1, color: 'rgba(58,77,233,0.3)' }
          ])
        }
      }
    ]
  }
  timeChartInstance.setOption(timeOption)
}

const updateBehaviorChart = (data) => {
  const chartData = data.map(item => ({
    value: item.count,
    name: getBehaviorLabel(item.behavior_type)
  }))
  
  behaviorChartInstance.setOption({
    series: [{
      data: chartData
    }]
  })
}

const updateDeviceChart = (data) => {
  const categories = data.map(item => getDeviceLabel(item.device_type))
  const values = data.map(item => item.count)
  
  deviceChartInstance.setOption({
    yAxis: {
      data: categories
    },
    series: [{
      data: values
    }]
  })
}

const updateTimeChart = (data) => {
  const hours = Array.from({ length: 24 }, (_, i) => i)
  const values = hours.map(hour => {
    const item = data.find(d => d.hour === hour)
    return item ? item.count : 0
  })
  
  timeChartInstance.setOption({
    series: [{
      data: values
    }]
  })
}

const getBehaviorLabel = (type) => {
  const labels = {
    'view': '浏览',
    'click': '点击',
    'search': '搜索',
    'add_cart': '加购',
    'remove_cart': '移除购物车',
    'purchase': '购买'
  }
  return labels[type] || type
}

const getDeviceLabel = (type) => {
  const labels = {
    'desktop': '桌面',
    'mobile': '移动',
    'tablet': '平板'
  }
  return labels[type] || type || '未知'
}

onMounted(async () => {
  await nextTick()
  initCharts()
  fetchData()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    behaviorChartInstance?.resize()
    deviceChartInstance?.resize()
    timeChartInstance?.resize()
  })
})
</script>

<style scoped>
.user-behavior {
  padding: 20px;
}

.el-row {
  margin-bottom: 20px;
}

.el-col {
  margin-bottom: 20px;
}
</style>