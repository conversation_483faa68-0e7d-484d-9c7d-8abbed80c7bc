<template>
  <div class="product-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>商品列表</span>
          <el-button type="primary" @click="handleAdd" v-if="userStore.userRole === 'admin'">
            <el-icon><Plus /></el-icon> 新增商品
          </el-button>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="商品名称">
          <el-input v-model="searchForm.search" placeholder="请输入商品名称" clearable />
        </el-form-item>
        <el-form-item label="商品分类">
          <el-select v-model="searchForm.category_id" placeholder="请选择分类" clearable>
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="价格范围">
          <el-input v-model="searchForm.min_price" placeholder="最低价" style="width: 100px" />
          <span style="margin: 0 5px">-</span>
          <el-input v-model="searchForm.max_price" placeholder="最高价" style="width: 100px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 商品表格 -->
      <el-table :data="products" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="商品图片" width="100">
          <template #default="scope">
            <el-image
              :src="scope.row.image_url || '/placeholder.png'"
              :preview-src-list="[scope.row.image_url]"
              :preview-teleported="true"
              style="width: 60px; height: 60px"
              fit="cover"
            />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="商品名称" min-width="150" />
        <el-table-column prop="category_name" label="分类" width="120" />
        <el-table-column prop="brand" label="品牌" width="100" />
        <el-table-column label="价格" width="120">
          <template #default="scope">
            <div class="price-cell">
              <span class="current-price">¥{{ scope.row.price }}</span>
              <span v-if="scope.row.original_price" class="original-price">
                ¥{{ scope.row.original_price }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="stock" label="库存" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.stock > 10 ? 'success' : scope.row.stock > 0 ? 'warning' : 'danger'">
              {{ scope.row.stock }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
              {{ scope.row.status === 'active' ? '在售' : '下架' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)" 
                      v-if="userStore.userRole === 'admin'">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)" 
                      v-if="userStore.userRole === 'admin'">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    
    <!-- 商品对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
      <el-form :model="productForm" :rules="productRules" ref="productFormRef" label-width="100px">
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="productForm.name" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品分类" prop="category">
          <el-select v-model="productForm.category" placeholder="请选择分类" style="width: 100%">
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="品牌">
          <el-input v-model="productForm.brand" placeholder="请输入品牌" />
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input-number v-model="productForm.price" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="原价">
          <el-input-number v-model="productForm.original_price" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="库存" prop="stock">
          <el-input-number v-model="productForm.stock" :min="0" />
        </el-form-item>
        <el-form-item label="商品图片">
          <el-input v-model="productForm.image_url" placeholder="请输入图片URL" />
        </el-form-item>
        <el-form-item label="商品描述">
          <el-input v-model="productForm.description" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="productForm.status">
            <el-radio value="active">在售</el-radio>
            <el-radio value="inactive">下架</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import request from '../../utils/request'
import { useUserStore } from '../../store/user'
import { trackBehavior, trackProduct, trackSearch } from '../../utils/tracker'

const userStore = useUserStore()

const products = ref([])
const categories = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  search: '',
  category_id: '',
  min_price: '',
  max_price: ''
})

const dialogVisible = ref(false)
const dialogTitle = ref('')
const productFormRef = ref()
const productForm = reactive({
  name: '',
  category: '',
  brand: '',
  price: 0,
  original_price: 0,
  stock: 0,
  image_url: '',
  description: '',
  status: 'active'
})

const productRules = {
  name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
  price: [{ required: true, message: '请输入价格', trigger: 'blur' }],
  stock: [{ required: true, message: '请输入库存', trigger: 'blur' }]
}

const fetchProducts = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...searchForm
    }
    const res = await request.get('/products', { params })
    products.value = res.data.products
    total.value = res.data.pagination.total_count
  } catch (error) {
    ElMessage.error('获取商品列表失败')
  } finally {
    loading.value = false
  }
}

const fetchCategories = async () => {
  try {
    const res = await request.get('/categories')
    categories.value = res.data
  } catch (error) {
    console.error('获取分类失败', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchProducts()
  // 追踪搜索行为
  if (searchForm.search) {
    trackSearch(searchForm.search, 'product')
  }
}

const resetSearch = () => {
  searchForm.search = ''
  searchForm.category_id = ''
  searchForm.min_price = ''
  searchForm.max_price = ''
  handleSearch()
}

const handleSizeChange = () => {
  currentPage.value = 1
  fetchProducts()
}

const handleCurrentChange = () => {
  fetchProducts()
}

const handleAdd = () => {
  dialogTitle.value = '新增商品'
  Object.keys(productForm).forEach(key => {
    if (key === 'status') {
      productForm[key] = 'active'
    } else if (key === 'price' || key === 'original_price' || key === 'stock') {
      productForm[key] = 0
    } else {
      productForm[key] = ''
    }
  })
  dialogVisible.value = true
}

const handleView = (row) => {
  // 追踪商品查看行为
  trackProduct('view', row.id, row.name, {
    category: row.category_name,
    price: row.price
  })
  ElMessage.info('查看商品详情功能开发中')
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑商品'
  Object.keys(productForm).forEach(key => {
    productForm[key] = row[key]
  })
  productForm.category = row.category
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该商品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await request.delete(`/products/${row.id}`)
    ElMessage.success('删除成功')
    fetchProducts()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  const valid = await productFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  try {
    if (dialogTitle.value === '新增商品') {
      await request.post('/products', productForm)
      ElMessage.success('新增成功')
    } else {
      await request.put(`/products/${productForm.id}`, productForm)
      ElMessage.success('编辑成功')
    }
    dialogVisible.value = false
    fetchProducts()
  } catch (error) {
    ElMessage.error(dialogTitle.value + '失败')
  }
}

onMounted(() => {
  fetchProducts()
  fetchCategories()
})
</script>

<style scoped>
.product-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.price-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.current-price {
  color: #f56c6c;
  font-weight: bold;
}

.original-price {
  color: #909399;
  text-decoration: line-through;
  font-size: 12px;
}

.el-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>