<template>
  <div class="product-categories">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>商品分类管理</span>
          <el-button type="primary" @click="showAddDialog">
            <el-icon><Plus /></el-icon> 新增分类
          </el-button>
        </div>
      </template>
      
      <!-- 搜索栏 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="分类名称">
          <el-input v-model="searchForm.name" placeholder="请输入分类名称" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部" clearable>
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 分类树形表格 -->
      <el-table
        :data="categoryList"
        style="width: 100%"
        row-key="id"
        v-loading="loading"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        default-expand-all
      >
        <el-table-column prop="name" label="分类名称" min-width="200">
          <template #default="scope">
            <div class="category-name">
              <el-icon v-if="scope.row.icon" class="category-icon">
                <component :is="scope.row.icon" />
              </el-icon>
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="分类编码" width="150" />
        <el-table-column prop="sort_order" label="排序" width="100">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.sort_order"
              :min="0"
              :max="999"
              size="small"
              @change="updateSortOrder(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="product_count" label="商品数量" width="100">
          <template #default="scope">
            <el-tag>{{ scope.row.product_count || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="active"
              inactive-value="inactive"
              @change="updateStatus(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="showEditDialog(scope.row)">编辑</el-button>
            <el-button size="small" type="primary" @click="showAddChildDialog(scope.row)">添加子分类</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 新增/编辑分类对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      :before-close="handleDialogClose"
    >
      <el-form :model="categoryForm" :rules="rules" ref="categoryFormRef" label-width="100px">
        <el-form-item label="上级分类" v-if="categoryForm.parent_id !== null">
          <el-cascader
            v-model="categoryForm.parent_id"
            :options="categoryOptions"
            :props="cascaderProps"
            placeholder="请选择上级分类"
            clearable
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类编码" prop="code">
          <el-input v-model="categoryForm.code" placeholder="请输入分类编码" />
        </el-form-item>
        <el-form-item label="分类图标">
          <el-select v-model="categoryForm.icon" placeholder="选择图标" clearable>
            <el-option label="购物袋" value="ShoppingBag" />
            <el-option label="礼品" value="Present" />
            <el-option label="手机" value="Cellphone" />
            <el-option label="电脑" value="Monitor" />
            <el-option label="相机" value="Camera" />
            <el-option label="手表" value="Watch" />
            <el-option label="衣服" value="Goods" />
            <el-option label="食品" value="Coffee" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类描述">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="categoryForm.sort_order" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="categoryForm.status">
            <el-radio value="active">启用</el-radio>
            <el-radio value="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="SEO标题">
          <el-input v-model="categoryForm.seo_title" placeholder="用于搜索引擎优化" />
        </el-form-item>
        <el-form-item label="SEO关键词">
          <el-input v-model="categoryForm.seo_keywords" placeholder="多个关键词用英文逗号分隔" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 批量操作 -->
    <el-dialog v-model="batchDialogVisible" title="批量操作" width="400px">
      <el-form :model="batchForm" label-width="100px">
        <el-form-item label="操作类型">
          <el-radio-group v-model="batchForm.action">
            <el-radio value="enable">批量启用</el-radio>
            <el-radio value="disable">批量禁用</el-radio>
            <el-radio value="delete">批量删除</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选中分类">
          <el-tag v-for="item in selectedCategories" :key="item.id" style="margin-right: 5px">
            {{ item.name }}
          </el-tag>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="executeBatchAction">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ShoppingBag, Present, Cellphone, Monitor, Camera, Watch, Goods, Coffee } from '@element-plus/icons-vue'
import request from '../../utils/request'

// 数据
const categoryList = ref([])
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const categoryFormRef = ref(null)
const batchDialogVisible = ref(false)
const selectedCategories = ref([])

// 搜索表单
const searchForm = reactive({
  name: '',
  status: ''
})

// 分类表单
const categoryForm = reactive({
  id: null,
  parent_id: null,
  name: '',
  code: '',
  icon: '',
  description: '',
  sort_order: 0,
  status: 'active',
  seo_title: '',
  seo_keywords: ''
})

// 批量操作表单
const batchForm = reactive({
  action: 'enable'
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入分类编码', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '只能包含大写字母、数字和下划线', trigger: 'blur' }
  ]
}

// 级联选择器配置
const cascaderProps = {
  value: 'id',
  label: 'name',
  children: 'children',
  checkStrictly: true
}

// 计算属性：级联选择器选项
const categoryOptions = computed(() => {
  const formatOptions = (list, excludeId = null) => {
    return list.filter(item => item.id !== excludeId).map(item => ({
      id: item.id,
      name: item.name,
      children: item.children ? formatOptions(item.children, excludeId) : []
    }))
  }
  return formatOptions(categoryList.value, categoryForm.id)
})

// 获取分类列表
const fetchCategories = async () => {
  loading.value = true
  try {
    const params = {
      name: searchForm.name,
      status: searchForm.status
    }
    const res = await request.get('/products/categories', { params })
    categoryList.value = buildTree(res.data || [])
  } catch (error) {
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 构建树形结构
const buildTree = (data) => {
  const map = {}
  const tree = []
  
  // 先创建所有节点的映射
  data.forEach(item => {
    map[item.id] = { ...item, children: [] }
  })
  
  // 构建树形结构
  data.forEach(item => {
    if (item.parent_id) {
      if (map[item.parent_id]) {
        map[item.parent_id].children.push(map[item.id])
      }
    } else {
      tree.push(map[item.id])
    }
  })
  
  return tree
}

// 搜索
const handleSearch = () => {
  fetchCategories()
}

// 重置搜索
const resetSearch = () => {
  searchForm.name = ''
  searchForm.status = ''
  fetchCategories()
}

// 显示新增对话框
const showAddDialog = () => {
  dialogTitle.value = '新增分类'
  resetForm()
  dialogVisible.value = true
}

// 显示添加子分类对话框
const showAddChildDialog = (row) => {
  dialogTitle.value = '添加子分类'
  resetForm()
  categoryForm.parent_id = row.id
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (row) => {
  dialogTitle.value = '编辑分类'
  Object.assign(categoryForm, {
    id: row.id,
    parent_id: row.parent_id,
    name: row.name,
    code: row.code,
    icon: row.icon,
    description: row.description,
    sort_order: row.sort_order,
    status: row.status,
    seo_title: row.seo_title,
    seo_keywords: row.seo_keywords
  })
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  categoryForm.id = null
  categoryForm.parent_id = null
  categoryForm.name = ''
  categoryForm.code = ''
  categoryForm.icon = ''
  categoryForm.description = ''
  categoryForm.sort_order = 0
  categoryForm.status = 'active'
  categoryForm.seo_title = ''
  categoryForm.seo_keywords = ''
  
  nextTick(() => {
    categoryFormRef.value?.clearValidate()
  })
}

// 提交表单
const submitForm = async () => {
  await categoryFormRef.value.validate()
  
  try {
    if (categoryForm.id) {
      // 编辑
      await request.put(`/products/categories/${categoryForm.id}`, categoryForm)
      ElMessage.success('更新成功')
    } else {
      // 新增
      await request.post('/products/categories', categoryForm)
      ElMessage.success('添加成功')
    }
    dialogVisible.value = false
    fetchCategories()
  } catch (error) {
    ElMessage.error(categoryForm.id ? '更新失败' : '添加失败')
  }
}

// 更新排序
const updateSortOrder = async (row) => {
  try {
    await request.patch(`/products/categories/${row.id}`, {
      sort_order: row.sort_order
    })
    ElMessage.success('排序更新成功')
  } catch (error) {
    ElMessage.error('排序更新失败')
  }
}

// 更新状态
const updateStatus = async (row) => {
  try {
    await request.patch(`/products/categories/${row.id}`, {
      status: row.status
    })
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
    // 恢复原状态
    row.status = row.status === 'active' ? 'inactive' : 'active'
  }
}

// 删除分类
const handleDelete = async (row) => {
  if (row.children && row.children.length > 0) {
    ElMessage.warning('请先删除子分类')
    return
  }
  
  if (row.product_count > 0) {
    ElMessage.warning('该分类下存在商品，无法删除')
    return
  }
  
  await ElMessageBox.confirm(
    `确定要删除分类"${row.name}"吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  
  try {
    await request.delete(`/products/categories/${row.id}`)
    ElMessage.success('删除成功')
    fetchCategories()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

// 执行批量操作
const executeBatchAction = async () => {
  if (selectedCategories.value.length === 0) {
    ElMessage.warning('请先选择分类')
    return
  }
  
  try {
    const ids = selectedCategories.value.map(item => item.id)
    await request.post('/products/categories/batch', {
      action: batchForm.action,
      ids
    })
    ElMessage.success('批量操作成功')
    batchDialogVisible.value = false
    fetchCategories()
  } catch (error) {
    ElMessage.error('批量操作失败')
  }
}

// 关闭对话框前的处理
const handleDialogClose = (done) => {
  categoryFormRef.value?.clearValidate()
  done()
}

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

onMounted(() => {
  fetchCategories()
})
</script>

<style scoped>
.product-categories {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.category-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-icon {
  color: #409eff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 树形表格缩进调整 */
:deep(.el-table__indent) {
  padding-left: 1em;
}

:deep(.el-table__expand-icon) {
  margin-right: 8px;
}
</style>