<template>
  <div class="profile-container">
    <el-container>
      <el-header class="profile-header">
        <div class="header-left">
          <el-button @click="$router.push('/dashboard')" :icon="ArrowLeft">返回首页</el-button>
          <h2>个人中心</h2>
        </div>
      </el-header>
      
      <el-main class="profile-main">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="user-info-card">
              <div class="avatar-section">
                <el-avatar :size="100" :src="userStore.user.avatar || ''" />
                <h3>{{ userStore.user.username }}</h3>
                <el-tag :type="userStore.userRole === 'admin' ? 'danger' : 'primary'">
                  {{ userStore.userRole === 'admin' ? '管理员' : '普通用户' }}
                </el-tag>
              </div>
              <el-divider />
              <div class="info-item">
                <span class="label">邮箱：</span>
                <span>{{ userStore.user.email }}</span>
              </div>
              <div class="info-item">
                <span class="label">注册时间：</span>
                <span>{{ formatDate(userStore.user.created_at) }}</span>
              </div>
              <div class="info-item">
                <span class="label">最后登录：</span>
                <span>{{ formatDate(userStore.user.last_login) }}</span>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="16">
            <el-card>
              <el-tabs v-model="activeTab">
                <el-tab-pane label="基本信息" name="info">
                  <el-form 
                    ref="infoFormRef"
                    :model="infoForm" 
                    :rules="infoRules"
                    label-width="100px"
                  >
                    <el-form-item label="真实姓名" prop="real_name">
                      <el-input v-model="infoForm.real_name" placeholder="请输入真实姓名" />
                    </el-form-item>
                    
                    <el-form-item label="手机号" prop="phone">
                      <el-input v-model="infoForm.phone" placeholder="请输入手机号" />
                    </el-form-item>
                    
                    <el-form-item label="头像URL" prop="avatar">
                      <el-input v-model="infoForm.avatar" placeholder="请输入头像URL" />
                    </el-form-item>
                    
                    <el-form-item>
                      <el-button type="primary" @click="updateProfile" :loading="infoLoading">
                        保存修改
                      </el-button>
                    </el-form-item>
                  </el-form>
                </el-tab-pane>
                
                <el-tab-pane label="修改密码" name="password">
                  <el-form 
                    ref="passwordFormRef"
                    :model="passwordForm" 
                    :rules="passwordRules"
                    label-width="100px"
                  >
                    <el-form-item label="原密码" prop="old_password">
                      <el-input 
                        v-model="passwordForm.old_password" 
                        type="password"
                        placeholder="请输入原密码"
                        show-password
                      />
                    </el-form-item>
                    
                    <el-form-item label="新密码" prop="new_password">
                      <el-input 
                        v-model="passwordForm.new_password" 
                        type="password"
                        placeholder="请输入新密码（6-20个字符）"
                        show-password
                      />
                    </el-form-item>
                    
                    <el-form-item label="确认密码" prop="confirm_password">
                      <el-input 
                        v-model="passwordForm.confirm_password" 
                        type="password"
                        placeholder="请再次输入新密码"
                        show-password
                      />
                    </el-form-item>
                    
                    <el-form-item>
                      <el-button type="primary" @click="changePassword" :loading="passwordLoading">
                        修改密码
                      </el-button>
                    </el-form-item>
                  </el-form>
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '../store/user'
import { ArrowLeft } from '@element-plus/icons-vue'

const userStore = useUserStore()
const activeTab = ref('info')
const infoFormRef = ref()
const passwordFormRef = ref()
const infoLoading = ref(false)
const passwordLoading = ref(false)

const infoForm = reactive({
  real_name: '',
  phone: '',
  avatar: ''
})

const passwordForm = reactive({
  old_password: '',
  new_password: '',
  confirm_password: ''
})

const validatePhone = (rule, value, callback) => {
  if (value && !/^1[3-9]\d{9}$/.test(value)) {
    callback(new Error('请输入有效的手机号码'))
  } else {
    callback()
  }
}

const validateConfirmPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请再次输入新密码'))
  } else if (value !== passwordForm.new_password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const infoRules = {
  phone: [
    { validator: validatePhone, trigger: 'blur' }
  ]
}

const passwordRules = {
  old_password: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在6到20个字符', trigger: 'blur' }
  ],
  confirm_password: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const updateProfile = async () => {
  const valid = await infoFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  infoLoading.value = true
  try {
    await userStore.updateProfile(infoForm)
    ElMessage.success('信息更新成功')
  } catch (error) {
    console.error('更新失败：', error)
  } finally {
    infoLoading.value = false
  }
}

const changePassword = async () => {
  const valid = await passwordFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  passwordLoading.value = true
  try {
    await userStore.changePassword(passwordForm)
    ElMessage.success('密码修改成功')
    passwordForm.old_password = ''
    passwordForm.new_password = ''
    passwordForm.confirm_password = ''
  } catch (error) {
    console.error('修改失败：', error)
  } finally {
    passwordLoading.value = false
  }
}

onMounted(async () => {
  try {
    await userStore.fetchUserInfo()
    infoForm.real_name = userStore.user.real_name || ''
    infoForm.phone = userStore.user.phone || ''
    infoForm.avatar = userStore.user.avatar || ''
  } catch (error) {
    console.error('获取用户信息失败：', error)
  }
})
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.profile-header {
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-left h2 {
  margin: 0;
  color: #333;
}

.profile-main {
  padding: 20px;
}

.user-info-card {
  text-align: center;
}

.avatar-section {
  padding: 20px;
}

.avatar-section h3 {
  margin: 15px 0 10px;
  color: #333;
}

.info-item {
  padding: 10px 20px;
  text-align: left;
  display: flex;
  justify-content: space-between;
}

.info-item .label {
  color: #999;
}
</style>