import request from './request'

class Tracker {
  constructor() {
    this.sessionId = this.getSessionId()
    this.queue = []
    this.timer = null
    this.pageStartTime = Date.now()
    this.isActive = true
    
    // 初始化事件监听
    this.init()
  }
  
  // 获取或创建会话ID
  getSessionId() {
    let sessionId = sessionStorage.getItem('session_id')
    if (!sessionId) {
      sessionId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      sessionStorage.setItem('session_id', sessionId)
    }
    return sessionId
  }
  
  // 初始化
  init() {
    // 页面加载完成
    window.addEventListener('load', () => {
      this.trackPageView()
    })
    
    // 页面卸载
    window.addEventListener('beforeunload', () => {
      this.sendData(true)
    })
    
    // 页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.isActive = false
        this.sendData()
      } else {
        this.isActive = true
        this.pageStartTime = Date.now()
      }
    })
    
    // 监听路由变化（Vue Router）
    if (window.$router) {
      window.$router.afterEach((to, from) => {
        this.trackPageView(to.fullPath)
      })
    }
    
    // 定时发送数据
    this.timer = setInterval(() => {
      this.sendData()
    }, 30000) // 每30秒发送一次
  }
  
  // 追踪页面访问
  trackPageView(url = window.location.href) {
    const data = {
      event_type: 'page_view',
      session_id: this.sessionId,
      page_url: url,
      page_title: document.title,
      referrer_url: document.referrer,
      timestamp: Date.now(),
      user_agent: navigator.userAgent,
      screen_resolution: `${screen.width}x${screen.height}`,
      viewport_size: `${window.innerWidth}x${window.innerHeight}`
    }
    
    this.addToQueue(data)
  }
  
  // 追踪用户行为
  trackBehavior(behaviorType, targetType = null, targetId = null, extraData = {}) {
    const data = {
      event_type: 'user_behavior',
      session_id: this.sessionId,
      behavior_type: behaviorType,
      target_type: targetType,
      target_id: targetId,
      page_url: window.location.href,
      timestamp: Date.now(),
      duration: this.isActive ? Math.floor((Date.now() - this.pageStartTime) / 1000) : 0,
      extra_data: extraData
    }
    
    this.addToQueue(data)
  }
  
  // 追踪点击事件
  trackClick(element, extraData = {}) {
    const data = {
      event_type: 'click',
      session_id: this.sessionId,
      element_id: element.id || '',
      element_class: element.className || '',
      element_text: element.textContent?.slice(0, 100) || '',
      element_tag: element.tagName,
      page_url: window.location.href,
      timestamp: Date.now(),
      position: {
        x: event.pageX,
        y: event.pageY
      },
      extra_data: extraData
    }
    
    this.addToQueue(data)
  }
  
  // 追踪搜索
  trackSearch(keyword, searchType = 'product', resultCount = null) {
    const data = {
      event_type: 'search',
      session_id: this.sessionId,
      search_keyword: keyword,
      search_type: searchType,
      result_count: resultCount,
      page_url: window.location.href,
      timestamp: Date.now()
    }
    
    this.addToQueue(data)
  }
  
  // 追踪商品事件
  trackProduct(action, productId, productName, extraData = {}) {
    this.trackBehavior(action, 'product', productId, {
      product_name: productName,
      ...extraData
    })
  }
  
  // 追踪购物车事件
  trackCart(action, productId, productName, quantity, price) {
    this.trackBehavior(action, 'product', productId, {
      product_name: productName,
      quantity: quantity,
      price: price
    })
  }
  
  // 追踪订单事件
  trackOrder(orderId, totalAmount, products) {
    this.trackBehavior('purchase', 'order', orderId, {
      total_amount: totalAmount,
      products: products
    })
  }
  
  // 追踪错误
  trackError(error, errorInfo = {}) {
    const data = {
      event_type: 'error',
      session_id: this.sessionId,
      error_message: error.message || error,
      error_stack: error.stack || '',
      page_url: window.location.href,
      timestamp: Date.now(),
      error_info: errorInfo
    }
    
    this.addToQueue(data)
  }
  
  // 添加到队列
  addToQueue(data) {
    // 添加用户信息
    const user = localStorage.getItem('user')
    if (user) {
      try {
        const userInfo = JSON.parse(user)
        data.user_id = userInfo.id
        data.username = userInfo.username
      } catch (e) {
        console.error('解析用户信息失败', e)
      }
    }
    
    // 添加设备信息
    data.device_type = this.getDeviceType()
    data.browser = this.getBrowser()
    data.os = this.getOS()
    
    this.queue.push(data)
    
    // 如果队列超过10条，立即发送
    if (this.queue.length >= 10) {
      this.sendData()
    }
  }
  
  // 发送数据
  async sendData(immediate = false) {
    if (this.queue.length === 0) return
    
    const data = [...this.queue]
    this.queue = []
    
    try {
      if (immediate) {
        // 使用 sendBeacon 发送数据（页面卸载时）
        const blob = new Blob([JSON.stringify({ events: data })], {
          type: 'application/json'
        })
        navigator.sendBeacon('/api/analytics/collect', blob)
      } else {
        // 正常发送
        await request.post('/analytics/collect', { events: data })
      }
    } catch (error) {
      console.error('发送追踪数据失败', error)
      // 失败的数据重新加入队列
      this.queue.unshift(...data)
    }
  }
  
  // 获取设备类型
  getDeviceType() {
    const userAgent = navigator.userAgent
    if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
      return 'tablet'
    }
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
      return 'mobile'
    }
    return 'desktop'
  }
  
  // 获取浏览器
  getBrowser() {
    const userAgent = navigator.userAgent
    if (userAgent.indexOf('Firefox') > -1) return 'Firefox'
    if (userAgent.indexOf('Chrome') > -1) return 'Chrome'
    if (userAgent.indexOf('Safari') > -1) return 'Safari'
    if (userAgent.indexOf('Opera') > -1) return 'Opera'
    if (userAgent.indexOf('MSIE') > -1 || userAgent.indexOf('Trident/') > -1) return 'IE'
    return 'Unknown'
  }
  
  // 获取操作系统
  getOS() {
    const userAgent = navigator.userAgent
    if (userAgent.indexOf('Windows') > -1) return 'Windows'
    if (userAgent.indexOf('Mac') > -1) return 'MacOS'
    if (userAgent.indexOf('Linux') > -1) return 'Linux'
    if (userAgent.indexOf('Android') > -1) return 'Android'
    if (userAgent.indexOf('iOS') > -1) return 'iOS'
    return 'Unknown'
  }
  
  // 销毁
  destroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    this.sendData()
  }
}

// 创建单例
const tracker = new Tracker()

// 导出追踪方法
export const trackPageView = (url) => tracker.trackPageView(url)
export const trackBehavior = (behaviorType, targetType, targetId, extraData) => 
  tracker.trackBehavior(behaviorType, targetType, targetId, extraData)
export const trackClick = (element, extraData) => tracker.trackClick(element, extraData)
export const trackSearch = (keyword, searchType, resultCount) => 
  tracker.trackSearch(keyword, searchType, resultCount)
export const trackProduct = (action, productId, productName, extraData) => 
  tracker.trackProduct(action, productId, productName, extraData)
export const trackCart = (action, productId, productName, quantity, price) => 
  tracker.trackCart(action, productId, productName, quantity, price)
export const trackOrder = (orderId, totalAmount, products) => 
  tracker.trackOrder(orderId, totalAmount, products)
export const trackError = (error, errorInfo) => tracker.trackError(error, errorInfo)

export default tracker