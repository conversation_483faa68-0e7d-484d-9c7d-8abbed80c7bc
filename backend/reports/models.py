from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class ReportTemplate(models.Model):
    """报表模板"""
    TEMPLATE_TYPE_CHOICES = [
        ('daily', '日报'),
        ('weekly', '周报'),
        ('monthly', '月报'),
        ('custom', '自定义')
    ]
    
    STATUS_CHOICES = [
        ('active', '激活'),
        ('inactive', '停用')
    ]
    
    id = models.AutoField(primary_key=True)
    template_name = models.CharField(max_length=100, verbose_name='模板名称')
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPE_CHOICES, verbose_name='模板类型')
    description = models.TextField(blank=True, null=True, verbose_name='模板描述')
    template_config = models.JSONField(verbose_name='模板配置')
    data_sources = models.JSONField(verbose_name='数据源配置')
    layout_config = models.JSO<PERSON>ield(verbose_name='布局配置')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='状态')
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='created_templates', verbose_name='创建人')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'report_templates'
        verbose_name = '报表模板'
        verbose_name_plural = '报表模板'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.template_name


class ReportInstance(models.Model):
    """报表实例"""
    GENERATION_STATUS_CHOICES = [
        ('pending', '待生成'),
        ('generating', '生成中'),
        ('completed', '已完成'),
        ('failed', '失败')
    ]
    
    id = models.BigAutoField(primary_key=True)
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE, related_name='instances', verbose_name='模板')
    report_name = models.CharField(max_length=100, verbose_name='报表名称')
    report_period = models.CharField(max_length=50, verbose_name='报表周期')
    start_date = models.DateField(verbose_name='统计开始日期')
    end_date = models.DateField(verbose_name='统计结束日期')
    report_data = models.JSONField(blank=True, null=True, verbose_name='报表数据')
    file_path = models.CharField(max_length=500, blank=True, null=True, verbose_name='文件路径')
    file_size = models.BigIntegerField(blank=True, null=True, verbose_name='文件大小')
    generation_status = models.CharField(max_length=20, choices=GENERATION_STATUS_CHOICES, default='pending', verbose_name='生成状态')
    error_message = models.TextField(blank=True, null=True, verbose_name='错误信息')
    generated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='generated_reports', verbose_name='生成人')
    generated_at = models.DateTimeField(blank=True, null=True, verbose_name='生成时间')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'report_instances'
        verbose_name = '报表实例'
        verbose_name_plural = '报表实例'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.report_name


class ReportSubscription(models.Model):
    """报表订阅"""
    SCHEDULE_TYPE_CHOICES = [
        ('daily', '每日'),
        ('weekly', '每周'),
        ('monthly', '每月')
    ]
    
    id = models.AutoField(primary_key=True)
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE, related_name='subscriptions', verbose_name='模板')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='report_subscriptions', verbose_name='用户')
    email = models.CharField(max_length=100, verbose_name='接收邮箱')
    schedule_type = models.CharField(max_length=20, choices=SCHEDULE_TYPE_CHOICES, verbose_name='调度类型')
    schedule_config = models.JSONField(verbose_name='调度配置')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    last_sent_at = models.DateTimeField(blank=True, null=True, verbose_name='最后发送时间')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'report_subscriptions'
        verbose_name = '报表订阅'
        verbose_name_plural = '报表订阅'
        unique_together = [['template', 'user']]
    
    def __str__(self):
        return f"{self.user.username} - {self.template.template_name}"


class ReportSendLog(models.Model):
    """报表发送记录"""
    SEND_STATUS_CHOICES = [
        ('pending', '待发送'),
        ('sent', '已发送'),
        ('failed', '失败')
    ]
    
    id = models.BigAutoField(primary_key=True)
    subscription = models.ForeignKey(ReportSubscription, on_delete=models.CASCADE, related_name='send_logs', verbose_name='订阅')
    report_instance = models.ForeignKey(ReportInstance, on_delete=models.CASCADE, related_name='send_logs', verbose_name='报表实例')
    recipient_email = models.CharField(max_length=100, verbose_name='接收邮箱')
    send_status = models.CharField(max_length=20, choices=SEND_STATUS_CHOICES, verbose_name='发送状态')
    error_message = models.TextField(blank=True, null=True, verbose_name='错误信息')
    sent_at = models.DateTimeField(blank=True, null=True, verbose_name='发送时间')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'report_send_logs'
        verbose_name = '报表发送记录'
        verbose_name_plural = '报表发送记录'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.report_instance.report_name} -> {self.recipient_email}"