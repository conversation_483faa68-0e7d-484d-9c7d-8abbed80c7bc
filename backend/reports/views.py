from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, Count, Avg
from django.http import HttpResponse, FileResponse
from django.utils import timezone
from datetime import datetime, timedelta
import json
import os

from .models import ReportTemplate, ReportInstance, ReportSubscription, ReportSendLog
from .serializers import (
    ReportTemplateSerializer, ReportInstanceSerializer,
    GenerateReportSerializer, PreviewReportSerializer,
    ReportSubscriptionSerializer, ReportSendLogSerializer
)


class ReportTemplateViewSet(viewsets.ModelViewSet):
    """报表模板视图集"""
    queryset = ReportTemplate.objects.all()
    serializer_class = ReportTemplateSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 筛选参数
        template_type = self.request.query_params.get('template_type')
        status = self.request.query_params.get('status')
        search = self.request.query_params.get('search')
        
        if template_type:
            queryset = queryset.filter(template_type=template_type)
        
        if status:
            queryset = queryset.filter(status=status)
        
        if search:
            queryset = queryset.filter(
                Q(template_name__icontains=search) |
                Q(description__icontains=search)
            )
        
        return queryset.select_related('created_by')
    
    @action(detail=True, methods=['post'])
    def generate(self, request, pk=None):
        """生成报表"""
        template = self.get_object()
        
        serializer = GenerateReportSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        data = serializer.validated_data
        
        # 创建报表实例
        report_name = data.get('report_name') or f"{template.template_name}_{data['start_date']}"
        
        instance = ReportInstance.objects.create(
            template=template,
            report_name=report_name,
            report_period=f"{data['start_date']} 至 {data['end_date']}",
            start_date=data['start_date'],
            end_date=data['end_date'],
            generation_status='pending',
            generated_by=request.user
        )
        
        # 这里应该触发异步任务生成报表
        # 暂时用同步方式模拟
        try:
            generator = ReportGenerator()
            report_data = generator.generate_mock_report(template, data['start_date'], data['end_date'])
            
            instance.report_data = report_data
            instance.generation_status = 'completed'
            instance.generated_at = timezone.now()
            instance.save()
            
        except Exception as e:
            instance.generation_status = 'failed'
            instance.error_message = str(e)
            instance.save()
        
        return Response({
            'report_instance_id': instance.id,
            'status': instance.generation_status,
            'estimated_time': 30
        })
    
    @action(detail=True, methods=['post'])
    def preview(self, request, pk=None):
        """预览报表"""
        template = self.get_object()
        
        serializer = PreviewReportSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        data = serializer.validated_data
        
        # 生成预览数据
        generator = ReportGenerator()
        preview_data = generator.generate_preview(template, data['start_date'], data['end_date'])
        
        return Response({
            'preview_data': preview_data,
            'expires_at': (timezone.now() + timedelta(hours=1)).isoformat()
        })


class ReportInstanceViewSet(viewsets.ModelViewSet):
    """报表实例视图集"""
    queryset = ReportInstance.objects.all()
    serializer_class = ReportInstanceSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 筛选参数
        template_id = self.request.query_params.get('template_id')
        status = self.request.query_params.get('status')
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if template_id:
            queryset = queryset.filter(template_id=template_id)
        
        if status:
            queryset = queryset.filter(generation_status=status)
        
        if start_date:
            queryset = queryset.filter(start_date__gte=start_date)
        
        if end_date:
            queryset = queryset.filter(end_date__lte=end_date)
        
        # 只返回当前用户生成的报表
        if not self.request.user.is_staff:
            queryset = queryset.filter(generated_by=self.request.user)
        
        return queryset.select_related('template', 'generated_by')
    
    @action(detail=True, methods=['get'])
    def status(self, request, pk=None):
        """获取报表生成状态"""
        instance = self.get_object()
        
        progress = 0
        if instance.generation_status == 'completed':
            progress = 100
        elif instance.generation_status == 'generating':
            progress = 50
        
        return Response({
            'instance_id': instance.id,
            'status': instance.generation_status,
            'progress': progress,
            'file_url': f"/api/reports/instances/{instance.id}/download" if instance.generation_status == 'completed' else None,
            'file_size': instance.file_size,
            'generated_at': instance.generated_at
        })
    
    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """下载报表"""
        instance = self.get_object()
        
        if instance.generation_status != 'completed':
            return Response(
                {'error': '报表尚未生成完成'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 这里应该返回实际的文件
        # 暂时返回模拟数据
        response = HttpResponse(
            json.dumps(instance.report_data, ensure_ascii=False, indent=2),
            content_type='application/json'
        )
        response['Content-Disposition'] = f'attachment; filename="{instance.report_name}.json"'
        
        return response
    
    @action(detail=True, methods=['get'])
    def data(self, request, pk=None):
        """获取报表数据"""
        instance = self.get_object()
        
        if instance.generation_status != 'completed':
            return Response(
                {'error': '报表尚未生成完成'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return Response({
            'report_info': {
                'id': instance.id,
                'report_name': instance.report_name,
                'period': instance.report_period,
                'generated_at': instance.generated_at
            },
            'sections': instance.report_data.get('sections', []) if instance.report_data else []
        })


class ReportSubscriptionViewSet(viewsets.ModelViewSet):
    """报表订阅视图集"""
    queryset = ReportSubscription.objects.all()
    serializer_class = ReportSubscriptionSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 只返回当前用户的订阅
        queryset = queryset.filter(user=self.request.user)
        
        return queryset.select_related('template', 'user')
    
    @action(detail=False, methods=['get'])
    def my(self, request):
        """获取我的订阅列表"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        
        return Response({
            'subscriptions': serializer.data
        })
    
    def destroy(self, request, *args, **kwargs):
        """取消订阅"""
        instance = self.get_object()
        
        # 只能取消自己的订阅
        if instance.user != request.user:
            return Response(
                {'error': '无权取消此订阅'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        instance.delete()
        
        return Response({'message': '订阅已取消'})


class ReportSendLogViewSet(viewsets.ReadOnlyModelViewSet):
    """报表发送记录视图集"""
    queryset = ReportSendLog.objects.all()
    serializer_class = ReportSendLogSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 筛选参数
        template_id = self.request.query_params.get('template_id')
        status = self.request.query_params.get('status')
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if template_id:
            queryset = queryset.filter(report_instance__template_id=template_id)
        
        if status:
            queryset = queryset.filter(send_status=status)
        
        if start_date:
            queryset = queryset.filter(created_at__gte=start_date)
        
        if end_date:
            queryset = queryset.filter(created_at__lte=end_date)
        
        # 只有管理员可以查看所有记录
        if not self.request.user.is_staff:
            queryset = queryset.filter(subscription__user=self.request.user)
        
        return queryset.select_related('subscription', 'report_instance')
    
    def list(self, request, *args, **kwargs):
        """获取发送记录列表"""
        queryset = self.filter_queryset(self.get_queryset())
        
        # 计算统计信息
        total_sent = queryset.filter(send_status='sent').count()
        total_failed = queryset.filter(send_status='failed').count()
        success_rate = (total_sent / (total_sent + total_failed) * 100) if (total_sent + total_failed) > 0 else 0
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response_data = {
                'logs': serializer.data,
                'summary': {
                    'total_sent': total_sent,
                    'success_rate': round(success_rate, 2),
                    'failed_count': total_failed
                }
            }
            return self.get_paginated_response(response_data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'logs': serializer.data,
            'summary': {
                'total_sent': total_sent,
                'success_rate': round(success_rate, 2),
                'failed_count': total_failed
            }
        })


# 报表生成器（简化版）
class ReportGenerator:
    """报表生成器"""
    
    def generate_mock_report(self, template, start_date, end_date):
        """生成模拟报表数据"""
        import random
        
        return {
            'report_title': template.template_name,
            'start_date': str(start_date),
            'end_date': str(end_date),
            'generated_at': datetime.now().isoformat(),
            'sections': [
                {
                    'type': 'metrics',
                    'title': '核心指标',
                    'data': {
                        'total_pv': random.randint(10000, 100000),
                        'total_uv': random.randint(1000, 10000),
                        'conversion_rate': round(random.random() * 0.1, 4),
                        'bounce_rate': round(random.random() * 0.5, 4)
                    }
                },
                {
                    'type': 'chart',
                    'title': '趋势分析',
                    'chart_type': 'line',
                    'data': {
                        'xAxis': [f"{i}:00" for i in range(24)],
                        'series': [
                            {
                                'name': 'PV',
                                'data': [random.randint(100, 1000) for _ in range(24)]
                            },
                            {
                                'name': 'UV',
                                'data': [random.randint(10, 100) for _ in range(24)]
                            }
                        ]
                    }
                },
                {
                    'type': 'table',
                    'title': '热门页面TOP10',
                    'data': {
                        'columns': ['排名', '页面', '访问量', '占比'],
                        'rows': [
                            [i+1, f"/page/{i+1}", random.randint(1000, 10000), f"{random.randint(5, 20)}%"]
                            for i in range(10)
                        ]
                    }
                }
            ]
        }
    
    def generate_preview(self, template, start_date, end_date):
        """生成预览数据"""
        return self.generate_mock_report(template, start_date, end_date)