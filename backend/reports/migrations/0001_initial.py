# Generated by Django 4.2.7 on 2025-08-11 08:50

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportInstance',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('report_name', models.CharField(max_length=100, verbose_name='报表名称')),
                ('report_period', models.CharField(max_length=50, verbose_name='报表周期')),
                ('start_date', models.DateField(verbose_name='统计开始日期')),
                ('end_date', models.DateField(verbose_name='统计结束日期')),
                ('report_data', models.JSONField(blank=True, null=True, verbose_name='报表数据')),
                ('file_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='文件路径')),
                ('file_size', models.BigIntegerField(blank=True, null=True, verbose_name='文件大小')),
                ('generation_status', models.CharField(choices=[('pending', '待生成'), ('generating', '生成中'), ('completed', '已完成'), ('failed', '失败')], default='pending', max_length=20, verbose_name='生成状态')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('generated_at', models.DateTimeField(blank=True, null=True, verbose_name='生成时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('generated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_reports', to=settings.AUTH_USER_MODEL, verbose_name='生成人')),
            ],
            options={
                'verbose_name': '报表实例',
                'verbose_name_plural': '报表实例',
                'db_table': 'report_instances',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportTemplate',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('template_name', models.CharField(max_length=100, verbose_name='模板名称')),
                ('template_type', models.CharField(choices=[('daily', '日报'), ('weekly', '周报'), ('monthly', '月报'), ('custom', '自定义')], max_length=20, verbose_name='模板类型')),
                ('description', models.TextField(blank=True, null=True, verbose_name='模板描述')),
                ('template_config', models.JSONField(verbose_name='模板配置')),
                ('data_sources', models.JSONField(verbose_name='数据源配置')),
                ('layout_config', models.JSONField(verbose_name='布局配置')),
                ('status', models.CharField(choices=[('active', '激活'), ('inactive', '停用')], default='active', max_length=20, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_templates', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '报表模板',
                'verbose_name_plural': '报表模板',
                'db_table': 'report_templates',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportSubscription',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('email', models.CharField(max_length=100, verbose_name='接收邮箱')),
                ('schedule_type', models.CharField(choices=[('daily', '每日'), ('weekly', '每周'), ('monthly', '每月')], max_length=20, verbose_name='调度类型')),
                ('schedule_config', models.JSONField(verbose_name='调度配置')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('last_sent_at', models.DateTimeField(blank=True, null=True, verbose_name='最后发送时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to='reports.reporttemplate', verbose_name='模板')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_subscriptions', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '报表订阅',
                'verbose_name_plural': '报表订阅',
                'db_table': 'report_subscriptions',
                'unique_together': {('template', 'user')},
            },
        ),
        migrations.CreateModel(
            name='ReportSendLog',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('recipient_email', models.CharField(max_length=100, verbose_name='接收邮箱')),
                ('send_status', models.CharField(choices=[('pending', '待发送'), ('sent', '已发送'), ('failed', '失败')], max_length=20, verbose_name='发送状态')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='发送时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('report_instance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='send_logs', to='reports.reportinstance', verbose_name='报表实例')),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='send_logs', to='reports.reportsubscription', verbose_name='订阅')),
            ],
            options={
                'verbose_name': '报表发送记录',
                'verbose_name_plural': '报表发送记录',
                'db_table': 'report_send_logs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='reportinstance',
            name='template',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='instances', to='reports.reporttemplate', verbose_name='模板'),
        ),
    ]
