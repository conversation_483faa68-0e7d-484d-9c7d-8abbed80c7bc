from rest_framework import serializers
from .models import ReportTemplate, ReportInstance, ReportSubscription, ReportSendLog
from users.serializers import UserSerializer


class ReportTemplateSerializer(serializers.ModelSerializer):
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    report_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ReportTemplate
        fields = [
            'id', 'template_name', 'template_type', 'description',
            'template_config', 'data_sources', 'layout_config',
            'status', 'created_by', 'created_by_name', 'created_at',
            'updated_at', 'report_count'
        ]
        read_only_fields = ['created_by', 'created_at', 'updated_at']
    
    def get_report_count(self, obj):
        return obj.instances.count()
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class ReportInstanceSerializer(serializers.ModelSerializer):
    template_name = serializers.Char<PERSON>ield(source='template.template_name', read_only=True)
    generated_by_name = serializers.CharField(source='generated_by.username', read_only=True)
    download_url = serializers.SerializerMethodField()
    
    class Meta:
        model = ReportInstance
        fields = [
            'id', 'template', 'template_name', 'report_name',
            'report_period', 'start_date', 'end_date', 'report_data',
            'file_path', 'file_size', 'generation_status', 'error_message',
            'generated_by', 'generated_by_name', 'generated_at',
            'created_at', 'download_url'
        ]
        read_only_fields = ['generated_by', 'generated_at', 'created_at']
    
    def get_download_url(self, obj):
        if obj.file_path and obj.generation_status == 'completed':
            request = self.context.get('request')
            if request:
                return f"/api/reports/instances/{obj.id}/download"
        return None


class GenerateReportSerializer(serializers.Serializer):
    template_id = serializers.IntegerField(required=True)
    start_date = serializers.DateField(required=True)
    end_date = serializers.DateField(required=True)
    report_name = serializers.CharField(required=False, allow_blank=True)
    export_format = serializers.ChoiceField(
        choices=['pdf', 'excel', 'html'],
        default='pdf',
        required=False
    )
    
    def validate(self, data):
        if data['end_date'] < data['start_date']:
            raise serializers.ValidationError("结束日期不能早于开始日期")
        
        try:
            template = ReportTemplate.objects.get(id=data['template_id'])
            if template.status != 'active':
                raise serializers.ValidationError("模板已停用")
        except ReportTemplate.DoesNotExist:
            raise serializers.ValidationError("模板不存在")
        
        return data


class PreviewReportSerializer(serializers.Serializer):
    template_id = serializers.IntegerField(required=True)
    start_date = serializers.DateField(required=True)
    end_date = serializers.DateField(required=True)
    preview_type = serializers.ChoiceField(
        choices=['html', 'thumbnail'],
        default='html',
        required=False
    )


class ReportSubscriptionSerializer(serializers.ModelSerializer):
    template_name = serializers.CharField(source='template.template_name', read_only=True)
    schedule_description = serializers.SerializerMethodField()
    
    class Meta:
        model = ReportSubscription
        fields = [
            'id', 'template', 'template_name', 'user', 'email',
            'schedule_type', 'schedule_config', 'is_active',
            'last_sent_at', 'created_at', 'updated_at', 'schedule_description'
        ]
        read_only_fields = ['user', 'last_sent_at', 'created_at', 'updated_at']
    
    def get_schedule_description(self, obj):
        schedule_type = obj.schedule_type
        config = obj.schedule_config
        
        if schedule_type == 'daily':
            time = config.get('time', '09:00')
            return f"每日 {time} 发送"
        elif schedule_type == 'weekly':
            day = config.get('day_of_week', 1)
            time = config.get('time', '09:00')
            days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            return f"每{days[day-1]} {time} 发送"
        elif schedule_type == 'monthly':
            day = config.get('day_of_month', 1)
            time = config.get('time', '09:00')
            return f"每月{day}日 {time} 发送"
        return ""
    
    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        if not validated_data.get('email'):
            validated_data['email'] = self.context['request'].user.email
        return super().create(validated_data)


class ReportSendLogSerializer(serializers.ModelSerializer):
    template_name = serializers.CharField(source='report_instance.template.template_name', read_only=True)
    report_name = serializers.CharField(source='report_instance.report_name', read_only=True)
    
    class Meta:
        model = ReportSendLog
        fields = [
            'id', 'subscription', 'report_instance', 'template_name',
            'report_name', 'recipient_email', 'send_status',
            'error_message', 'sent_at', 'created_at'
        ]
        read_only_fields = ['created_at']