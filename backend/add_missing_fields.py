import pymysql

# 连接数据库
connection = pymysql.connect(
    host='localhost',
    user='root',
    password='12345678',
    database='ecommerce_analytics',
    charset='utf8mb4'
)

try:
    with connection.cursor() as cursor:
        # 检查并添加缺失的字段
        check_sql = "DESCRIBE users"
        cursor.execute(check_sql)
        existing_columns = [row[0] for row in cursor.fetchall()]
        print("现有字段:", existing_columns)
        
        # 需要添加的字段
        fields_to_add = [
            ("is_active", "BOOLEAN NOT NULL DEFAULT TRUE"),
            ("is_staff", "BOOLEAN NOT NULL DEFAULT FALSE"),
            ("is_superuser", "BOOLEAN NOT NULL DEFAULT FALSE"),
        ]
        
        for field_name, field_def in fields_to_add:
            if field_name not in existing_columns:
                sql = f"ALTER TABLE users ADD COLUMN {field_name} {field_def};"
                try:
                    cursor.execute(sql)
                    print(f"添加字段成功: {field_name}")
                except Exception as e:
                    print(f"添加字段失败 {field_name}: {e}")
            else:
                print(f"字段已存在: {field_name}")
        
        connection.commit()
        print("\n数据库修复完成！")
        
except Exception as e:
    print(f"错误: {e}")
finally:
    connection.close()