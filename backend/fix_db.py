import pymysql

# 连接数据库
connection = pymysql.connect(
    host='localhost',
    user='root',
    password='12345678',
    database='ecommerce_analytics',
    charset='utf8mb4'
)

try:
    with connection.cursor() as cursor:
        # 添加Django权限系统需要的字段
        sql_commands = [
            "ALTER TABLE users ADD COLUMN is_superuser BOOLEAN NOT NULL DEFAULT FALSE;",
            "ALTER TABLE users ADD COLUMN is_staff BOOLEAN NOT NULL DEFAULT FALSE;",
            "ALTER TABLE users ADD COLUMN groups TEXT;",
            "ALTER TABLE users ADD COLUMN user_permissions TEXT;"
        ]
        
        for sql in sql_commands:
            try:
                cursor.execute(sql)
                print(f"执行成功: {sql}")
            except Exception as e:
                print(f"跳过 (可能已存在): {sql[:50]}... - {e}")
        
        connection.commit()
        print("数据库修复完成")
        
except Exception as e:
    print(f"错误: {e}")
finally:
    connection.close()