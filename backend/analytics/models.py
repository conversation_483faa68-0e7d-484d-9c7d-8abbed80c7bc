from django.db import models
from django.contrib.auth import get_user_model
from products.models import Product

User = get_user_model()


class UserBehavior(models.Model):
    BEHAVIOR_TYPES = [
        ('view', '浏览'),
        ('click', '点击'),
        ('search', '搜索'),
        ('add_cart', '加购'),
        ('remove_cart', '移除购物车'),
        ('purchase', '购买')
    ]
    
    TARGET_TYPES = [
        ('product', '商品'),
        ('category', '分类'),
        ('page', '页面')
    ]
    
    DEVICE_TYPES = [
        ('desktop', '桌面'),
        ('mobile', '移动'),
        ('tablet', '平板')
    ]
    
    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                            related_name='behaviors', verbose_name='用户')
    session_id = models.CharField(max_length=100, verbose_name='会话ID')
    behavior_type = models.CharField(max_length=20, choices=BEHAVIOR_TYPES, verbose_name='行为类型')
    target_type = models.CharField(max_length=20, choices=TARGET_TYPES, 
                                  blank=True, null=True, verbose_name='目标类型')
    target_id = models.IntegerField(blank=True, null=True, verbose_name='目标ID')
    page_url = models.CharField(max_length=500, blank=True, null=True, verbose_name='页面URL')
    referrer_url = models.CharField(max_length=500, blank=True, null=True, verbose_name='来源URL')
    ip_address = models.CharField(max_length=45, blank=True, null=True, verbose_name='IP地址')
    user_agent = models.TextField(blank=True, null=True, verbose_name='用户代理')
    device_type = models.CharField(max_length=20, choices=DEVICE_TYPES, 
                                  blank=True, null=True, verbose_name='设备类型')
    browser = models.CharField(max_length=50, blank=True, null=True, verbose_name='浏览器')
    os = models.CharField(max_length=50, blank=True, null=True, verbose_name='操作系统')
    location = models.CharField(max_length=100, blank=True, null=True, verbose_name='地理位置')
    duration = models.IntegerField(blank=True, null=True, verbose_name='停留时长(秒)')
    extra_data = models.JSONField(blank=True, null=True, verbose_name='额外数据')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'user_behaviors'
        verbose_name = '用户行为'
        verbose_name_plural = '用户行为'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['session_id']),
            models.Index(fields=['behavior_type']),
            models.Index(fields=['target_type', 'target_id']),
        ]
    
    def __str__(self):
        return f"{self.user or 'Anonymous'} - {self.behavior_type} - {self.created_at}"


class PageView(models.Model):
    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                            related_name='page_views', verbose_name='用户')
    session_id = models.CharField(max_length=100, verbose_name='会话ID')
    page_url = models.CharField(max_length=500, verbose_name='页面URL')
    page_title = models.CharField(max_length=200, blank=True, null=True, verbose_name='页面标题')
    referrer_url = models.CharField(max_length=500, blank=True, null=True, verbose_name='来源URL')
    ip_address = models.CharField(max_length=45, blank=True, null=True, verbose_name='IP地址')
    user_agent = models.TextField(blank=True, null=True, verbose_name='用户代理')
    load_time = models.IntegerField(blank=True, null=True, verbose_name='页面加载时间(毫秒)')
    stay_time = models.IntegerField(blank=True, null=True, verbose_name='页面停留时间(秒)')
    scroll_depth = models.DecimalField(max_digits=5, decimal_places=2, 
                                       blank=True, null=True, verbose_name='滚动深度百分比')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'page_views'
        verbose_name = '页面访问'
        verbose_name_plural = '页面访问'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['session_id']),
            models.Index(fields=['page_url']),
        ]
    
    def __str__(self):
        return f"{self.page_url} - {self.created_at}"


class SearchLog(models.Model):
    SEARCH_TYPES = [
        ('product', '商品'),
        ('category', '分类'),
        ('article', '文章')
    ]
    
    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                            related_name='search_logs', verbose_name='用户')
    session_id = models.CharField(max_length=100, verbose_name='会话ID')
    search_keyword = models.CharField(max_length=200, verbose_name='搜索关键词')
    search_type = models.CharField(max_length=20, choices=SEARCH_TYPES, 
                                  default='product', verbose_name='搜索类型')
    result_count = models.IntegerField(blank=True, null=True, verbose_name='结果数量')
    click_position = models.IntegerField(blank=True, null=True, verbose_name='点击位置')
    click_product = models.ForeignKey(Product, on_delete=models.SET_NULL, 
                                     null=True, blank=True, verbose_name='点击商品')
    ip_address = models.CharField(max_length=45, blank=True, null=True, verbose_name='IP地址')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'search_logs'
        verbose_name = '搜索记录'
        verbose_name_plural = '搜索记录'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['session_id']),
            models.Index(fields=['search_keyword']),
        ]
    
    def __str__(self):
        return f"{self.search_keyword} - {self.created_at}"
