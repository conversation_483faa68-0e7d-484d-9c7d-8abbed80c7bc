from rest_framework import serializers
from .models import User<PERSON>eh<PERSON>or, PageView, SearchLog
from products.models import Product


class UserBehaviorSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserBehavior
        fields = ['id', 'user', 'session_id', 'behavior_type', 'target_type', 
                 'target_id', 'page_url', 'referrer_url', 'ip_address', 
                 'user_agent', 'device_type', 'browser', 'os', 'location', 
                 'duration', 'extra_data', 'created_at']
        read_only_fields = ['id', 'created_at']
    
    def create(self, validated_data):
        # 自动获取用户信息
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            validated_data['user'] = request.user
        
        # 自动获取IP地址
        if request:
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip = x_forwarded_for.split(',')[0]
            else:
                ip = request.META.get('REMOTE_ADDR')
            validated_data['ip_address'] = ip
            
            # 自动获取User Agent
            validated_data['user_agent'] = request.META.get('HTTP_USER_AGENT')
        
        return super().create(validated_data)


class PageViewSerializer(serializers.ModelSerializer):
    class Meta:
        model = PageView
        fields = ['id', 'user', 'session_id', 'page_url', 'page_title', 
                 'referrer_url', 'ip_address', 'user_agent', 'load_time', 
                 'stay_time', 'scroll_depth', 'created_at']
        read_only_fields = ['id', 'created_at']
    
    def create(self, validated_data):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            validated_data['user'] = request.user
        
        if request:
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip = x_forwarded_for.split(',')[0]
            else:
                ip = request.META.get('REMOTE_ADDR')
            validated_data['ip_address'] = ip
            validated_data['user_agent'] = request.META.get('HTTP_USER_AGENT')
        
        return super().create(validated_data)


class SearchLogSerializer(serializers.ModelSerializer):
    click_product_name = serializers.CharField(source='click_product.name', read_only=True)
    
    class Meta:
        model = SearchLog
        fields = ['id', 'user', 'session_id', 'search_keyword', 'search_type', 
                 'result_count', 'click_position', 'click_product', 
                 'click_product_name', 'ip_address', 'created_at']
        read_only_fields = ['id', 'created_at']
    
    def create(self, validated_data):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            validated_data['user'] = request.user
        
        if request:
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip = x_forwarded_for.split(',')[0]
            else:
                ip = request.META.get('REMOTE_ADDR')
            validated_data['ip_address'] = ip
        
        return super().create(validated_data)


class BatchEventSerializer(serializers.Serializer):
    events = serializers.ListField(child=serializers.DictField())
    
    def validate_events(self, value):
        if not value:
            raise serializers.ValidationError("事件列表不能为空")
        
        for event in value:
            if 'type' not in event:
                raise serializers.ValidationError("每个事件必须包含type字段")
            if 'data' not in event:
                raise serializers.ValidationError("每个事件必须包含data字段")
            if event['type'] not in ['behavior', 'pageview', 'search']:
                raise serializers.ValidationError(f"不支持的事件类型: {event['type']}")
        
        return value


class RealtimeStatsSerializer(serializers.Serializer):
    online_users = serializers.IntegerField()
    page_views_today = serializers.IntegerField()
    orders_today = serializers.IntegerField()
    revenue_today = serializers.DecimalField(max_digits=10, decimal_places=2)
    top_pages = serializers.ListField(child=serializers.DictField())
    top_products = serializers.ListField(child=serializers.DictField())