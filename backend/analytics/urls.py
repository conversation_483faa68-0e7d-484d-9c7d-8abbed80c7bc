from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import (
    UserBehaviorViewSet,
    PageViewViewSet,
    SearchLogViewSet,
    batch_collect,
    realtime_stats,
    import_data,
    user_behavior_analysis
)

router = DefaultRouter()
router.register('behavior', UserBehaviorViewSet, basename='behavior')
router.register('pageview', PageViewViewSet, basename='pageview')
router.register('search', SearchLogViewSet, basename='search')

app_name = 'analytics'

urlpatterns = [
    path('', include(router.urls)),
    path('batch/', batch_collect, name='batch_collect'),
    path('realtime/', realtime_stats, name='realtime_stats'),
    path('import/', import_data, name='import_data'),
    path('behavior-analysis/', user_behavior_analysis, name='behavior_analysis'),
]