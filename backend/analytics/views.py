from rest_framework import viewsets, status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from django.db.models import Count, Sum, Q, F
from django.utils import timezone
from datetime import datetime, timedelta
from .models import UserBehavior, PageView, SearchLog
from products.models import Product, Order
from .serializers import (
    UserBehaviorSerializer,
    PageViewSerializer,
    SearchLogSerializer,
    BatchEventSerializer,
    RealtimeStatsSerializer
)
import json


class UserBehaviorViewSet(viewsets.ModelViewSet):
    queryset = UserBehavior.objects.all()
    serializer_class = UserBehaviorSerializer
    permission_classes = [AllowAny]  # 允许匿名用户记录行为
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        behavior = serializer.save()
        
        return Response({
            'code': 200,
            'message': '数据采集成功',
            'data': {
                'behavior_id': behavior.id
            }
        }, status=status.HTTP_201_CREATED)


class PageViewViewSet(viewsets.ModelViewSet):
    queryset = PageView.objects.all()
    serializer_class = PageViewSerializer
    permission_classes = [AllowAny]
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        pageview = serializer.save()
        
        return Response({
            'code': 200,
            'message': '页面访问记录成功',
            'data': {
                'pageview_id': pageview.id
            }
        }, status=status.HTTP_201_CREATED)


class SearchLogViewSet(viewsets.ModelViewSet):
    queryset = SearchLog.objects.all()
    serializer_class = SearchLogSerializer
    permission_classes = [AllowAny]
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        search_log = serializer.save()
        
        return Response({
            'code': 200,
            'message': '搜索记录成功',
            'data': {
                'search_id': search_log.id
            }
        }, status=status.HTTP_201_CREATED)


@api_view(['POST'])
@permission_classes([AllowAny])
def batch_collect(request):
    """批量数据采集接口"""
    serializer = BatchEventSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'code': 400,
            'message': '数据验证失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    events = serializer.validated_data['events']
    success_count = 0
    failed_count = 0
    failed_items = []
    
    for index, event in enumerate(events):
        try:
            event_type = event['type']
            event_data = event['data']
            
            if event_type == 'behavior':
                behavior_serializer = UserBehaviorSerializer(
                    data=event_data, 
                    context={'request': request}
                )
                if behavior_serializer.is_valid():
                    behavior_serializer.save()
                    success_count += 1
                else:
                    failed_count += 1
                    failed_items.append({
                        'index': index,
                        'error': behavior_serializer.errors
                    })
            
            elif event_type == 'pageview':
                pageview_serializer = PageViewSerializer(
                    data=event_data,
                    context={'request': request}
                )
                if pageview_serializer.is_valid():
                    pageview_serializer.save()
                    success_count += 1
                else:
                    failed_count += 1
                    failed_items.append({
                        'index': index,
                        'error': pageview_serializer.errors
                    })
            
            elif event_type == 'search':
                search_serializer = SearchLogSerializer(
                    data=event_data,
                    context={'request': request}
                )
                if search_serializer.is_valid():
                    search_serializer.save()
                    success_count += 1
                else:
                    failed_count += 1
                    failed_items.append({
                        'index': index,
                        'error': search_serializer.errors
                    })
            
        except Exception as e:
            failed_count += 1
            failed_items.append({
                'index': index,
                'error': str(e)
            })
    
    return Response({
        'code': 200,
        'message': '批量数据处理成功',
        'data': {
            'success_count': success_count,
            'failed_count': failed_count,
            'failed_items': failed_items
        }
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def realtime_stats(request):
    """实时数据统计接口"""
    now = timezone.now()
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
    
    # 计算在线用户数（最近5分钟有活动的用户）
    five_minutes_ago = now - timedelta(minutes=5)
    online_users = UserBehavior.objects.filter(
        created_at__gte=five_minutes_ago
    ).values('user').distinct().count()
    
    # 今日页面访问量
    page_views_today = PageView.objects.filter(
        created_at__gte=today_start
    ).count()
    
    # 今日订单数
    orders_today = Order.objects.filter(
        created_at__gte=today_start
    ).count()
    
    # 今日营收
    revenue_today = Order.objects.filter(
        created_at__gte=today_start,
        order_status__in=['paid', 'shipped', 'completed']
    ).aggregate(total=Sum('total_amount'))['total'] or 0
    
    # 热门页面TOP5
    top_pages = PageView.objects.filter(
        created_at__gte=today_start
    ).values('page_url').annotate(
        views=Count('id')
    ).order_by('-views')[:5]
    
    # 热门商品TOP5（基于点击行为）
    top_products_ids = UserBehavior.objects.filter(
        created_at__gte=today_start,
        behavior_type='click',
        target_type='product'
    ).values('target_id').annotate(
        clicks=Count('id')
    ).order_by('-clicks')[:5]
    
    top_products = []
    for item in top_products_ids:
        try:
            product = Product.objects.get(id=item['target_id'])
            top_products.append({
                'product_id': product.id,
                'product_name': product.name,
                'clicks': item['clicks']
            })
        except Product.DoesNotExist:
            pass
    
    data = {
        'online_users': online_users,
        'page_views_today': page_views_today,
        'orders_today': orders_today,
        'revenue_today': float(revenue_today),
        'top_pages': list(top_pages),
        'top_products': top_products
    }
    
    return Response({
        'code': 200,
        'message': '获取成功',
        'data': data
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def import_data(request):
    """数据导入接口（管理员权限）"""
    if request.user.role != 'admin':
        return Response({
            'code': 403,
            'message': '无权限操作'
        }, status=status.HTTP_403_FORBIDDEN)
    
    file = request.FILES.get('file')
    data_type = request.POST.get('data_type')
    mapping = request.POST.get('mapping')
    
    if not file:
        return Response({
            'code': 400,
            'message': '请上传文件'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    if data_type not in ['behavior', 'pageview', 'search', 'product', 'order']:
        return Response({
            'code': 400,
            'message': '不支持的数据类型'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # 这里可以添加实际的文件处理逻辑
    # 例如：CSV/Excel文件解析和数据导入
    
    # 模拟导入结果
    import_result = {
        'total_rows': 1000,
        'success_rows': 950,
        'failed_rows': 50,
        'import_id': f'import_{datetime.now().strftime("%Y%m%d%H%M%S")}',
        'errors': [
            {'row': 5, 'error': '缺少必填字段：user_id'},
            {'row': 23, 'error': '数据格式错误'}
        ][:5]  # 最多返回5个错误示例
    }
    
    return Response({
        'code': 200,
        'message': '数据导入成功',
        'data': import_result
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_behavior_analysis(request):
    """用户行为分析接口"""
    user_id = request.query_params.get('user_id')
    start_date = request.query_params.get('start_date')
    end_date = request.query_params.get('end_date')
    
    queryset = UserBehavior.objects.all()
    
    if user_id:
        queryset = queryset.filter(user_id=user_id)
    
    if start_date:
        queryset = queryset.filter(created_at__gte=start_date)
    
    if end_date:
        queryset = queryset.filter(created_at__lte=end_date)
    
    # 行为类型分布
    behavior_distribution = queryset.values('behavior_type').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # 设备类型分布
    device_distribution = queryset.values('device_type').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # 时间分布（按小时）
    time_distribution = queryset.extra(
        select={'hour': 'HOUR(created_at)'}
    ).values('hour').annotate(
        count=Count('id')
    ).order_by('hour')
    
    return Response({
        'code': 200,
        'message': '获取成功',
        'data': {
            'behavior_distribution': list(behavior_distribution),
            'device_distribution': list(device_distribution),
            'time_distribution': list(time_distribution)
        }
    })
