# Generated by Django 4.2.7 on 2025-08-11 08:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserBehavior',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('session_id', models.CharField(max_length=100, verbose_name='会话ID')),
                ('behavior_type', models.CharField(choices=[('view', '浏览'), ('click', '点击'), ('search', '搜索'), ('add_cart', '加购'), ('remove_cart', '移除购物车'), ('purchase', '购买')], max_length=20, verbose_name='行为类型')),
                ('target_type', models.Char<PERSON><PERSON>(blank=True, choices=[('product', '商品'), ('category', '分类'), ('page', '页面')], max_length=20, null=True, verbose_name='目标类型')),
                ('target_id', models.IntegerField(blank=True, null=True, verbose_name='目标ID')),
                ('page_url', models.CharField(blank=True, max_length=500, null=True, verbose_name='页面URL')),
                ('referrer_url', models.CharField(blank=True, max_length=500, null=True, verbose_name='来源URL')),
                ('ip_address', models.CharField(blank=True, max_length=45, null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='用户代理')),
                ('device_type', models.CharField(blank=True, choices=[('desktop', '桌面'), ('mobile', '移动'), ('tablet', '平板')], max_length=20, null=True, verbose_name='设备类型')),
                ('browser', models.CharField(blank=True, max_length=50, null=True, verbose_name='浏览器')),
                ('os', models.CharField(blank=True, max_length=50, null=True, verbose_name='操作系统')),
                ('location', models.CharField(blank=True, max_length=100, null=True, verbose_name='地理位置')),
                ('duration', models.IntegerField(blank=True, null=True, verbose_name='停留时长(秒)')),
                ('extra_data', models.JSONField(blank=True, null=True, verbose_name='额外数据')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='behaviors', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户行为',
                'verbose_name_plural': '用户行为',
                'db_table': 'user_behaviors',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'created_at'], name='user_behavi_user_id_9bb153_idx'), models.Index(fields=['session_id'], name='user_behavi_session_9ea4bd_idx'), models.Index(fields=['behavior_type'], name='user_behavi_behavio_78e108_idx'), models.Index(fields=['target_type', 'target_id'], name='user_behavi_target__34b43a_idx')],
            },
        ),
        migrations.CreateModel(
            name='SearchLog',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('session_id', models.CharField(max_length=100, verbose_name='会话ID')),
                ('search_keyword', models.CharField(max_length=200, verbose_name='搜索关键词')),
                ('search_type', models.CharField(choices=[('product', '商品'), ('category', '分类'), ('article', '文章')], default='product', max_length=20, verbose_name='搜索类型')),
                ('result_count', models.IntegerField(blank=True, null=True, verbose_name='结果数量')),
                ('click_position', models.IntegerField(blank=True, null=True, verbose_name='点击位置')),
                ('ip_address', models.CharField(blank=True, max_length=45, null=True, verbose_name='IP地址')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('click_product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='products.product', verbose_name='点击商品')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='search_logs', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '搜索记录',
                'verbose_name_plural': '搜索记录',
                'db_table': 'search_logs',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'created_at'], name='search_logs_user_id_f35125_idx'), models.Index(fields=['session_id'], name='search_logs_session_621029_idx'), models.Index(fields=['search_keyword'], name='search_logs_search__3748b8_idx')],
            },
        ),
        migrations.CreateModel(
            name='PageView',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('session_id', models.CharField(max_length=100, verbose_name='会话ID')),
                ('page_url', models.CharField(max_length=500, verbose_name='页面URL')),
                ('page_title', models.CharField(blank=True, max_length=200, null=True, verbose_name='页面标题')),
                ('referrer_url', models.CharField(blank=True, max_length=500, null=True, verbose_name='来源URL')),
                ('ip_address', models.CharField(blank=True, max_length=45, null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='用户代理')),
                ('load_time', models.IntegerField(blank=True, null=True, verbose_name='页面加载时间(毫秒)')),
                ('stay_time', models.IntegerField(blank=True, null=True, verbose_name='页面停留时间(秒)')),
                ('scroll_depth', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='滚动深度百分比')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='page_views', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '页面访问',
                'verbose_name_plural': '页面访问',
                'db_table': 'page_views',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'created_at'], name='page_views_user_id_0cec42_idx'), models.Index(fields=['session_id'], name='page_views_session_108156_idx'), models.Index(fields=['page_url'], name='page_views_page_ur_a48fb0_idx')],
            },
        ),
    ]
