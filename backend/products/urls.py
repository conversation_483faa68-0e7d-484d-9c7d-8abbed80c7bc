from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import ProductCategoryViewSet, ProductViewSet, OrderViewSet

router = DefaultRouter()
router.register('categories', ProductCategoryViewSet, basename='category')
router.register('products', ProductViewSet, basename='product')
router.register('orders', OrderViewSet, basename='order')

app_name = 'products'

urlpatterns = [
    path('', include(router.urls)),
]