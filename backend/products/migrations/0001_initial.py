# Generated by Django 4.2.7 on 2025-08-11 08:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('order_no', models.CharField(max_length=50, unique=True, verbose_name='订单号')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='订单总额')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='优惠金额')),
                ('shipping_fee', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='运费')),
                ('payment_method', models.CharField(blank=True, choices=[('alipay', '支付宝'), ('wechat', '微信支付'), ('credit_card', '信用卡')], max_length=20, null=True, verbose_name='支付方式')),
                ('order_status', models.CharField(choices=[('pending', '待支付'), ('paid', '已支付'), ('shipped', '已发货'), ('completed', '已完成'), ('cancelled', '已取消')], default='pending', max_length=20, verbose_name='订单状态')),
                ('shipping_address', models.TextField(blank=True, null=True, verbose_name='收货地址')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='orders', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '订单',
                'verbose_name_plural': '订单',
                'db_table': 'orders',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, verbose_name='分类名称')),
                ('level', models.IntegerField(default=1, verbose_name='分类级别')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序序号')),
                ('status', models.CharField(choices=[('active', '活跃'), ('inactive', '未激活')], default='active', max_length=20, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children', to='products.productcategory', verbose_name='父分类')),
            ],
            options={
                'verbose_name': '商品分类',
                'verbose_name_plural': '商品分类',
                'db_table': 'product_categories',
                'ordering': ['sort_order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='商品名称')),
                ('brand', models.CharField(blank=True, max_length=100, null=True, verbose_name='品牌')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='价格')),
                ('original_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='原价')),
                ('description', models.TextField(blank=True, null=True, verbose_name='商品描述')),
                ('image_url', models.CharField(blank=True, max_length=500, null=True, verbose_name='商品图片')),
                ('stock', models.IntegerField(default=0, verbose_name='库存')),
                ('status', models.CharField(choices=[('active', '在售'), ('inactive', '下架'), ('deleted', '已删除')], default='active', max_length=20, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='products', to='products.productcategory', verbose_name='商品分类')),
            ],
            options={
                'verbose_name': '商品',
                'verbose_name_plural': '商品',
                'db_table': 'products',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('product_name', models.CharField(max_length=200, verbose_name='商品名称')),
                ('quantity', models.IntegerField(verbose_name='购买数量')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='单价')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='小计')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='products.order', verbose_name='订单')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='order_items', to='products.product', verbose_name='商品')),
            ],
            options={
                'verbose_name': '订单详情',
                'verbose_name_plural': '订单详情',
                'db_table': 'order_items',
            },
        ),
    ]
