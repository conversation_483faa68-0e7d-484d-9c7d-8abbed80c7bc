from rest_framework import serializers
from .models import ProductCategory, Product, Order, OrderItem


class ProductCategorySerializer(serializers.ModelSerializer):
    children = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductCategory
        fields = ['id', 'name', 'parent', 'level', 'sort_order', 
                 'status', 'children', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']
    
    def get_children(self, obj):
        children = obj.children.filter(status='active')
        if children:
            return ProductCategorySerializer(children, many=True).data
        return []


class ProductSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    
    class Meta:
        model = Product
        fields = ['id', 'name', 'category', 'category_name', 'brand', 
                 'price', 'original_price', 'description', 'image_url', 
                 'stock', 'status', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']


class ProductCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = ['name', 'category', 'brand', 'price', 'original_price', 
                 'description', 'image_url', 'stock', 'status']
    
    def validate_price(self, value):
        if value <= 0:
            raise serializers.ValidationError("价格必须大于0")
        return value
    
    def validate_stock(self, value):
        if value < 0:
            raise serializers.ValidationError("库存不能为负数")
        return value


class OrderItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_image = serializers.CharField(source='product.image_url', read_only=True)
    
    class Meta:
        model = OrderItem
        fields = ['id', 'product', 'product_name', 'product_image', 
                 'quantity', 'unit_price', 'total_price', 'created_at']
        read_only_fields = ['created_at']


class OrderSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True, read_only=True)
    username = serializers.CharField(source='user.username', read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    
    class Meta:
        model = Order
        fields = ['id', 'order_no', 'user', 'username', 'user_email',
                 'total_amount', 'discount_amount', 'shipping_fee', 
                 'payment_method', 'order_status', 'shipping_address', 
                 'items', 'created_at', 'updated_at']
        read_only_fields = ['order_no', 'created_at', 'updated_at']


class OrderCreateSerializer(serializers.ModelSerializer):
    items = serializers.ListField(child=serializers.DictField(), write_only=True)
    
    class Meta:
        model = Order
        fields = ['total_amount', 'discount_amount', 'shipping_fee', 
                 'payment_method', 'shipping_address', 'items']
    
    def create(self, validated_data):
        items_data = validated_data.pop('items')
        user = self.context['request'].user
        
        # 生成订单号
        import datetime
        import random
        order_no = f"ORD{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}{random.randint(100, 999)}"
        
        # 创建订单
        order = Order.objects.create(
            order_no=order_no,
            user=user,
            **validated_data
        )
        
        # 创建订单详情
        for item_data in items_data:
            product = Product.objects.get(id=item_data['product_id'])
            OrderItem.objects.create(
                order=order,
                product=product,
                product_name=product.name,
                quantity=item_data['quantity'],
                unit_price=product.price,
                total_price=product.price * item_data['quantity']
            )
        
        return order