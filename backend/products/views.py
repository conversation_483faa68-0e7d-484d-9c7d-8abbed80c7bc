from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.db.models import Q, Count, Sum
from .models import ProductCategory, Product, Order, OrderItem
from .serializers import (
    ProductCategorySerializer,
    ProductSerializer,
    ProductCreateSerializer,
    OrderSerializer,
    OrderCreateSerializer
)


class ProductCategoryViewSet(viewsets.ModelViewSet):
    queryset = ProductCategory.objects.filter(status='active')
    serializer_class = ProductCategorySerializer
    permission_classes = [AllowAny]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        parent_id = self.request.query_params.get('parent_id')
        
        if parent_id:
            if parent_id == '0':
                queryset = queryset.filter(parent__isnull=True)
            else:
                queryset = queryset.filter(parent_id=parent_id)
        else:
            # 默认只返回顶级分类
            queryset = queryset.filter(parent__isnull=True)
        
        return queryset.order_by('sort_order', 'id')
    
    @action(detail=False, methods=['get'])
    def tree(self, request):
        """获取分类树形结构"""
        categories = ProductCategory.objects.filter(
            status='active', parent__isnull=True
        ).order_by('sort_order', 'id')
        serializer = self.get_serializer(categories, many=True)
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })


class ProductViewSet(viewsets.ModelViewSet):
    queryset = Product.objects.filter(status='active')
    permission_classes = [AllowAny]
    
    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return ProductCreateSerializer
        return ProductSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 分类筛选
        category_id = self.request.query_params.get('category_id')
        if category_id:
            queryset = queryset.filter(category_id=category_id)
        
        # 搜索
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | 
                Q(brand__icontains=search) |
                Q(description__icontains=search)
            )
        
        # 价格范围
        min_price = self.request.query_params.get('min_price')
        max_price = self.request.query_params.get('max_price')
        if min_price:
            queryset = queryset.filter(price__gte=min_price)
        if max_price:
            queryset = queryset.filter(price__lte=max_price)
        
        # 排序
        ordering = self.request.query_params.get('ordering', '-created_at')
        queryset = queryset.order_by(ordering)
        
        return queryset
    
    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response = self.get_paginated_response(serializer.data)
            return Response({
                'code': 200,
                'message': '获取成功',
                'data': {
                    'products': serializer.data,
                    'pagination': {
                        'current_page': response.data.get('current_page', 1),
                        'total_pages': response.data.get('total_pages', 1),
                        'total_count': response.data.get('count', 0),
                        'per_page': self.paginator.page_size
                    }
                }
            })
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'products': serializer.data
            }
        })
    
    def create(self, request, *args, **kwargs):
        # 只有管理员可以创建商品
        if not request.user.is_authenticated or request.user.role != 'admin':
            return Response({
                'code': 403,
                'message': '无权限操作'
            }, status=status.HTTP_403_FORBIDDEN)
        
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        
        return Response({
            'code': 200,
            'message': '商品创建成功',
            'data': serializer.data
        }, status=status.HTTP_201_CREATED)
    
    @action(detail=False, methods=['get'])
    def hot(self, request):
        """获取热门商品"""
        # 这里可以根据实际的热度算法来排序
        products = self.get_queryset()[:10]
        serializer = self.get_serializer(products, many=True)
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })


class OrderViewSet(viewsets.ModelViewSet):
    serializer_class = OrderSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        queryset = Order.objects.all()
        
        # 普通用户只能看自己的订单
        if user.role != 'admin':
            queryset = queryset.filter(user=user)
        else:
            # 管理员可以筛选用户
            user_id = self.request.query_params.get('user_id')
            if user_id:
                queryset = queryset.filter(user_id=user_id)
        
        # 状态筛选
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(order_status=status_filter)
        
        # 日期筛选
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(created_at__gte=start_date)
        if end_date:
            queryset = queryset.filter(created_at__lte=end_date)
        
        return queryset.order_by('-created_at')
    
    def get_serializer_class(self):
        if self.action == 'create':
            return OrderCreateSerializer
        return OrderSerializer
    
    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response = self.get_paginated_response(serializer.data)
            return Response({
                'code': 200,
                'message': '获取成功',
                'data': {
                    'orders': serializer.data,
                    'pagination': {
                        'current_page': response.data.get('current_page', 1),
                        'total_pages': response.data.get('total_pages', 1),
                        'total_count': response.data.get('count', 0),
                        'per_page': self.paginator.page_size
                    }
                }
            })
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'orders': serializer.data
            }
        })
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        order = serializer.save()
        
        # 返回完整的订单信息
        output_serializer = OrderSerializer(order)
        return Response({
            'code': 200,
            'message': '订单创建成功',
            'data': output_serializer.data
        }, status=status.HTTP_201_CREATED)
    
    @action(detail=True, methods=['put'])
    def update_status(self, request, pk=None):
        """更新订单状态"""
        order = self.get_object()
        new_status = request.data.get('status')
        
        if new_status not in ['pending', 'paid', 'shipped', 'completed', 'cancelled']:
            return Response({
                'code': 400,
                'message': '无效的订单状态'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查状态转换的合法性
        if order.order_status == 'completed' or order.order_status == 'cancelled':
            return Response({
                'code': 400,
                'message': '已完成或已取消的订单不能修改状态'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        order.order_status = new_status
        order.save()
        
        serializer = self.get_serializer(order)
        return Response({
            'code': 200,
            'message': '订单状态更新成功',
            'data': serializer.data
        })
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取订单统计信息"""
        user = request.user
        
        if user.role == 'admin':
            # 管理员看所有订单统计
            orders = Order.objects.all()
        else:
            # 普通用户看自己的订单统计
            orders = Order.objects.filter(user=user)
        
        stats = {
            'total_orders': orders.count(),
            'total_amount': orders.aggregate(Sum('total_amount'))['total_amount__sum'] or 0,
            'pending_orders': orders.filter(order_status='pending').count(),
            'paid_orders': orders.filter(order_status='paid').count(),
            'completed_orders': orders.filter(order_status='completed').count(),
            'cancelled_orders': orders.filter(order_status='cancelled').count(),
        }
        
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': stats
        })
