from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class ProductCategory(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100, verbose_name='分类名称')
    parent = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, 
                              related_name='children', verbose_name='父分类')
    level = models.IntegerField(default=1, verbose_name='分类级别')
    sort_order = models.IntegerField(default=0, verbose_name='排序序号')
    status = models.CharField(max_length=20, choices=[
        ('active', '活跃'),
        ('inactive', '未激活')
    ], default='active', verbose_name='状态')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'product_categories'
        verbose_name = '商品分类'
        verbose_name_plural = '商品分类'
        ordering = ['sort_order', 'id']
    
    def __str__(self):
        return self.name


class Product(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200, verbose_name='商品名称')
    category = models.ForeignKey(ProductCategory, on_delete=models.PROTECT, 
                                related_name='products', verbose_name='商品分类')
    brand = models.CharField(max_length=100, blank=True, null=True, verbose_name='品牌')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='价格')
    original_price = models.DecimalField(max_digits=10, decimal_places=2, 
                                        blank=True, null=True, verbose_name='原价')
    description = models.TextField(blank=True, null=True, verbose_name='商品描述')
    image_url = models.CharField(max_length=500, blank=True, null=True, verbose_name='商品图片')
    stock = models.IntegerField(default=0, verbose_name='库存')
    status = models.CharField(max_length=20, choices=[
        ('active', '在售'),
        ('inactive', '下架'),
        ('deleted', '已删除')
    ], default='active', verbose_name='状态')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'products'
        verbose_name = '商品'
        verbose_name_plural = '商品'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name


class Order(models.Model):
    id = models.BigAutoField(primary_key=True)
    order_no = models.CharField(max_length=50, unique=True, verbose_name='订单号')
    user = models.ForeignKey(User, on_delete=models.PROTECT, 
                            related_name='orders', verbose_name='用户')
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='订单总额')
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, 
                                         default=0, verbose_name='优惠金额')
    shipping_fee = models.DecimalField(max_digits=10, decimal_places=2, 
                                       default=0, verbose_name='运费')
    payment_method = models.CharField(max_length=20, choices=[
        ('alipay', '支付宝'),
        ('wechat', '微信支付'),
        ('credit_card', '信用卡')
    ], blank=True, null=True, verbose_name='支付方式')
    order_status = models.CharField(max_length=20, choices=[
        ('pending', '待支付'),
        ('paid', '已支付'),
        ('shipped', '已发货'),
        ('completed', '已完成'),
        ('cancelled', '已取消')
    ], default='pending', verbose_name='订单状态')
    shipping_address = models.TextField(blank=True, null=True, verbose_name='收货地址')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'orders'
        verbose_name = '订单'
        verbose_name_plural = '订单'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.order_no


class OrderItem(models.Model):
    id = models.BigAutoField(primary_key=True)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, 
                             related_name='items', verbose_name='订单')
    product = models.ForeignKey(Product, on_delete=models.PROTECT, 
                               related_name='order_items', verbose_name='商品')
    product_name = models.CharField(max_length=200, verbose_name='商品名称')
    quantity = models.IntegerField(verbose_name='购买数量')
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='单价')
    total_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='小计')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'order_items'
        verbose_name = '订单详情'
        verbose_name_plural = '订单详情'
    
    def __str__(self):
        return f"{self.order.order_no} - {self.product_name}"
