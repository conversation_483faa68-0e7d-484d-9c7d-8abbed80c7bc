# Generated by Django 4.2.7 on 2025-08-11 08:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.Char<PERSON>ield(max_length=128, verbose_name='password')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('username', models.CharField(max_length=50, unique=True, verbose_name='用户名')),
                ('email', models.EmailField(max_length=100, unique=True, verbose_name='邮箱')),
                ('real_name', models.Char<PERSON>ield(blank=True, max_length=50, null=True, verbose_name='真实姓名')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='手机号')),
                ('avatar', models.CharField(blank=True, max_length=255, null=True, verbose_name='头像URL')),
                ('role', models.CharField(choices=[('admin', '管理员'), ('user', '普通用户')], default='user', max_length=10, verbose_name='角色')),
                ('status', models.CharField(choices=[('active', '活跃'), ('inactive', '未激活'), ('banned', '已禁用')], default='active', max_length=10, verbose_name='状态')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='最后登录时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_staff', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
                'db_table': 'users',
            },
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('session_key', models.CharField(max_length=255, verbose_name='会话密钥')),
                ('ip_address', models.CharField(blank=True, max_length=45, null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='用户代理')),
                ('expires_at', models.DateTimeField(verbose_name='过期时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户会话',
                'verbose_name_plural': '用户会话',
                'db_table': 'user_sessions',
            },
        ),
    ]
