from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from datetime import datetime

from .serializers import (
    UserRegisterSerializer,
    UserLoginSerializer,
    UserSerializer,
    UserUpdateSerializer,
    ChangePasswordSerializer
)
from .authentication import generate_jwt_token

User = get_user_model()


@api_view(['POST'])
@permission_classes([AllowAny])
def register(request):
    serializer = UserRegisterSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.save()
        token = generate_jwt_token(user)
        
        return Response({
            'code': 200,
            'message': '注册成功',
            'data': {
                'user_id': user.id,
                'username': user.username,
                'email': user.email,
                'token': token
            }
        }, status=status.HTTP_201_CREATED)
    
    return Response({
        'code': 400,
        'message': '注册失败',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def login(request):
    serializer = UserLoginSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']
        token = generate_jwt_token(user)
        
        # 更新最后登录时间
        user.last_login = datetime.now()
        user.save(update_fields=['last_login'])
        
        user_data = UserSerializer(user).data
        
        return Response({
            'code': 200,
            'message': '登录成功',
            'data': {
                'user': user_data,
                'token': token,
                'expires_in': 3600 * 24  # 24小时
            }
        })
    
    return Response({
        'code': 400,
        'message': '登录失败',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout(request):
    # 这里可以添加清除session或其他清理操作
    return Response({
        'code': 200,
        'message': '登出成功'
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_current_user(request):
    user_data = UserSerializer(request.user).data
    return Response({
        'code': 200,
        'message': '获取成功',
        'data': user_data
    })


@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_profile(request):
    serializer = UserUpdateSerializer(request.user, data=request.data, partial=True)
    if serializer.is_valid():
        serializer.save()
        user_data = UserSerializer(request.user).data
        return Response({
            'code': 200,
            'message': '更新成功',
            'data': user_data
        })
    
    return Response({
        'code': 400,
        'message': '更新失败',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def change_password(request):
    serializer = ChangePasswordSerializer(data=request.data)
    if serializer.is_valid():
        user = request.user
        
        # 验证原密码
        if not user.check_password(serializer.validated_data['old_password']):
            return Response({
                'code': 400,
                'message': '原密码错误'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 设置新密码
        user.set_password(serializer.validated_data['new_password'])
        user.save()
        
        return Response({
            'code': 200,
            'message': '密码修改成功'
        })
    
    return Response({
        'code': 400,
        'message': '密码修改失败',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)
