-- ===========================================
-- 基于大数据的电商用户行为分析系统
-- 数据库初始化脚本
-- 创建时间: 2024-01-15
-- MySQL版本: 8.0+
-- ===========================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS ecommerce_analytics 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE ecommerce_analytics;

-- ===========================================
-- 1. 用户管理模块表
-- ===========================================

-- 用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID，主键',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名，唯一',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱，唯一',
    password VARCHAR(128) NOT NULL COMMENT '密码（加密存储）',
    real_name VARCHAR(50) NULL COMMENT '真实姓名',
    phone VARCHAR(20) NULL COMMENT '手机号',
    avatar VARCHAR(255) NULL COMMENT '头像URL',
    role ENUM('admin', 'user') NOT NULL DEFAULT 'user' COMMENT '用户角色：admin/user',
    status ENUM('active', 'inactive', 'banned') NOT NULL DEFAULT 'active' COMMENT '用户状态：active/inactive/banned',
    last_login DATETIME NULL COMMENT '最后登录时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 用户会话表
CREATE TABLE user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '会话ID，主键',
    user_id INT NOT NULL COMMENT '用户ID，外键',
    session_key VARCHAR(255) NOT NULL COMMENT '会话密钥',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- ===========================================
-- 2. 基础数据模块表
-- ===========================================

-- 商品分类表
CREATE TABLE product_categories (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID，主键',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    parent_id INT NULL COMMENT '父分类ID',
    level INT NOT NULL DEFAULT 1 COMMENT '分类级别',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序序号',
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (parent_id) REFERENCES product_categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 商品表
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '商品ID，主键',
    name VARCHAR(200) NOT NULL COMMENT '商品名称',
    category_id INT NOT NULL COMMENT '分类ID',
    brand VARCHAR(100) NULL COMMENT '品牌',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    original_price DECIMAL(10,2) NULL COMMENT '原价',
    description TEXT NULL COMMENT '商品描述',
    image_url VARCHAR(500) NULL COMMENT '商品图片',
    stock INT NOT NULL DEFAULT 0 COMMENT '库存',
    status ENUM('active', 'inactive', 'deleted') NOT NULL DEFAULT 'active' COMMENT '状态：active/inactive/deleted',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (category_id) REFERENCES product_categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 订单表
CREATE TABLE orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '订单ID，主键',
    order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号，唯一',
    user_id INT NOT NULL COMMENT '用户ID，外键',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总额',
    discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '优惠金额',
    shipping_fee DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '运费',
    payment_method ENUM('alipay', 'wechat', 'credit_card') NULL COMMENT '支付方式：alipay/wechat/credit_card',
    order_status ENUM('pending', 'paid', 'shipped', 'completed', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '订单状态：pending/paid/shipped/completed/cancelled',
    shipping_address TEXT NULL COMMENT '收货地址',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 订单详情表
CREATE TABLE order_items (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '详情ID，主键',
    order_id BIGINT NOT NULL COMMENT '订单ID，外键',
    product_id INT NOT NULL COMMENT '商品ID，外键',
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    quantity INT NOT NULL COMMENT '购买数量',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    total_price DECIMAL(10,2) NOT NULL COMMENT '小计',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单详情表';

-- ===========================================
-- 3. 数据采集模块表
-- ===========================================

-- 用户行为表
CREATE TABLE user_behaviors (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '行为ID，主键',
    user_id INT NULL COMMENT '用户ID，外键',
    session_id VARCHAR(100) NOT NULL COMMENT '会话ID',
    behavior_type ENUM('view', 'click', 'search', 'add_cart', 'remove_cart', 'purchase') NOT NULL COMMENT '行为类型：view/click/search/add_cart/remove_cart/purchase',
    target_type ENUM('product', 'category', 'page') NULL COMMENT '目标类型：product/category/page',
    target_id INT NULL COMMENT '目标ID',
    page_url VARCHAR(500) NULL COMMENT '页面URL',
    referrer_url VARCHAR(500) NULL COMMENT '来源URL',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    device_type ENUM('desktop', 'mobile', 'tablet') NULL COMMENT '设备类型：desktop/mobile/tablet',
    browser VARCHAR(50) NULL COMMENT '浏览器',
    os VARCHAR(50) NULL COMMENT '操作系统',
    location VARCHAR(100) NULL COMMENT '地理位置',
    duration INT NULL COMMENT '停留时长（秒）',
    extra_data JSON NULL COMMENT '额外数据',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户行为表';

-- 页面访问表
CREATE TABLE page_views (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '访问ID，主键',
    user_id INT NULL COMMENT '用户ID，外键',
    session_id VARCHAR(100) NOT NULL COMMENT '会话ID',
    page_url VARCHAR(500) NOT NULL COMMENT '页面URL',
    page_title VARCHAR(200) NULL COMMENT '页面标题',
    referrer_url VARCHAR(500) NULL COMMENT '来源URL',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    load_time INT NULL COMMENT '页面加载时间（毫秒）',
    stay_time INT NULL COMMENT '页面停留时间（秒）',
    scroll_depth DECIMAL(5,2) NULL COMMENT '滚动深度百分比',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='页面访问表';

-- 搜索记录表
CREATE TABLE search_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '搜索ID，主键',
    user_id INT NULL COMMENT '用户ID，外键',
    session_id VARCHAR(100) NOT NULL COMMENT '会话ID',
    search_keyword VARCHAR(200) NOT NULL COMMENT '搜索关键词',
    search_type ENUM('product', 'category', 'article') NOT NULL DEFAULT 'product' COMMENT '搜索类型：product/category/article',
    result_count INT NULL COMMENT '结果数量',
    click_position INT NULL COMMENT '点击位置',
    click_product_id INT NULL COMMENT '点击商品ID',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (click_product_id) REFERENCES products(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索记录表';

-- ===========================================
-- 4. 数据分析模块表
-- ===========================================

-- 分析结果表
CREATE TABLE analysis_results (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '结果ID，主键',
    analysis_type VARCHAR(50) NOT NULL COMMENT '分析类型',
    analysis_name VARCHAR(100) NOT NULL COMMENT '分析名称',
    date_range_start DATE NOT NULL COMMENT '分析开始日期',
    date_range_end DATE NOT NULL COMMENT '分析结束日期',
    result_data JSON NOT NULL COMMENT '分析结果数据',
    status ENUM('pending', 'completed', 'failed') NOT NULL DEFAULT 'pending' COMMENT '状态：pending/completed/failed',
    created_by INT NOT NULL COMMENT '创建人ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析结果表';

-- 用户标签表
CREATE TABLE user_tags (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID，主键',
    user_id INT NOT NULL COMMENT '用户ID，外键',
    tag_type VARCHAR(50) NOT NULL COMMENT '标签类型',
    tag_value VARCHAR(100) NOT NULL COMMENT '标签值',
    score DECIMAL(5,2) NULL COMMENT '标签分数',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_tag (user_id, tag_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户标签表';

-- 商品热度表
CREATE TABLE product_popularity (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID，主键',
    product_id INT NOT NULL COMMENT '商品ID，外键',
    date DATE NOT NULL COMMENT '统计日期',
    view_count INT NOT NULL DEFAULT 0 COMMENT '浏览次数',
    click_count INT NOT NULL DEFAULT 0 COMMENT '点击次数',
    cart_count INT NOT NULL DEFAULT 0 COMMENT '加购次数',
    order_count INT NOT NULL DEFAULT 0 COMMENT '下单次数',
    conversion_rate DECIMAL(5,4) NOT NULL DEFAULT 0 COMMENT '转化率',
    popularity_score DECIMAL(8,2) NOT NULL DEFAULT 0 COMMENT '热度分数',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE KEY uk_product_date (product_id, date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品热度表';

-- 漏斗分析表
CREATE TABLE funnel_analysis (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '分析ID，主键',
    funnel_name VARCHAR(100) NOT NULL COMMENT '漏斗名称',
    step_name VARCHAR(100) NOT NULL COMMENT '步骤名称',
    step_order INT NOT NULL COMMENT '步骤顺序',
    date DATE NOT NULL COMMENT '统计日期',
    user_count INT NOT NULL DEFAULT 0 COMMENT '用户数量',
    conversion_rate DECIMAL(5,4) NOT NULL DEFAULT 0 COMMENT '转化率',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_funnel_step_date (funnel_name, step_order, date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='漏斗分析表';

-- ===========================================
-- 5. 数据可视化模块表
-- ===========================================

-- 仪表盘配置表
CREATE TABLE dashboard_configs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID，主键',
    name VARCHAR(100) NOT NULL COMMENT '仪表盘名称',
    description TEXT NULL COMMENT '描述',
    layout_config JSON NOT NULL COMMENT '布局配置',
    widgets JSON NOT NULL COMMENT '组件配置',
    is_public BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开',
    created_by INT NOT NULL COMMENT '创建人ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='仪表盘配置表';

-- 图表配置表
CREATE TABLE chart_configs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '图表ID，主键',
    chart_name VARCHAR(100) NOT NULL COMMENT '图表名称',
    chart_type ENUM('line', 'bar', 'pie', 'scatter', 'heatmap') NOT NULL COMMENT '图表类型：line/bar/pie/scatter/heatmap',
    data_source VARCHAR(100) NOT NULL COMMENT '数据源',
    query_config JSON NOT NULL COMMENT '查询配置',
    style_config JSON NOT NULL COMMENT '样式配置',
    refresh_interval INT NOT NULL DEFAULT 300 COMMENT '刷新间隔（秒）',
    created_by INT NOT NULL COMMENT '创建人ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图表配置表';

-- 实时数据缓存表
CREATE TABLE realtime_data_cache (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '缓存ID，主键',
    metric_key VARCHAR(100) NOT NULL COMMENT '指标键值',
    metric_value DECIMAL(15,4) NOT NULL COMMENT '指标值',
    data_type ENUM('counter', 'gauge', 'histogram') NOT NULL COMMENT '数据类型：counter/gauge/histogram',
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    metadata JSON NULL COMMENT '元数据',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    INDEX idx_metric_key (metric_key),
    INDEX idx_timestamp (timestamp),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实时数据缓存表';

-- ===========================================
-- 6. 报表管理模块表
-- ===========================================

-- 报表模板表
CREATE TABLE report_templates (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '模板ID，主键',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_type ENUM('daily', 'weekly', 'monthly', 'custom') NOT NULL COMMENT '模板类型：daily/weekly/monthly/custom',
    description TEXT NULL COMMENT '模板描述',
    template_config JSON NOT NULL COMMENT '模板配置',
    data_sources JSON NOT NULL COMMENT '数据源配置',
    layout_config JSON NOT NULL COMMENT '布局配置',
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active/inactive',
    created_by INT NOT NULL COMMENT '创建人ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报表模板表';

-- 报表实例表
CREATE TABLE report_instances (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '实例ID，主键',
    template_id INT NOT NULL COMMENT '模板ID，外键',
    report_name VARCHAR(100) NOT NULL COMMENT '报表名称',
    report_period VARCHAR(50) NOT NULL COMMENT '报表周期',
    start_date DATE NOT NULL COMMENT '统计开始日期',
    end_date DATE NOT NULL COMMENT '统计结束日期',
    report_data JSON NULL COMMENT '报表数据',
    file_path VARCHAR(500) NULL COMMENT '文件路径',
    file_size BIGINT NULL COMMENT '文件大小',
    generation_status ENUM('pending', 'generating', 'completed', 'failed') NOT NULL DEFAULT 'pending' COMMENT '生成状态：pending/generating/completed/failed',
    error_message TEXT NULL COMMENT '错误信息',
    generated_by INT NOT NULL COMMENT '生成人ID',
    generated_at DATETIME NULL COMMENT '生成时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (template_id) REFERENCES report_templates(id),
    FOREIGN KEY (generated_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报表实例表';

-- 报表订阅表
CREATE TABLE report_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '订阅ID，主键',
    template_id INT NOT NULL COMMENT '模板ID，外键',
    user_id INT NOT NULL COMMENT '用户ID，外键',
    email VARCHAR(100) NOT NULL COMMENT '接收邮箱',
    schedule_type ENUM('daily', 'weekly', 'monthly') NOT NULL COMMENT '调度类型：daily/weekly/monthly',
    schedule_config JSON NOT NULL COMMENT '调度配置',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    last_sent_at DATETIME NULL COMMENT '最后发送时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (template_id) REFERENCES report_templates(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报表订阅表';

-- 报表发送记录表
CREATE TABLE report_send_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID，主键',
    subscription_id INT NOT NULL COMMENT '订阅ID，外键',
    report_instance_id BIGINT NOT NULL COMMENT '报表实例ID，外键',
    recipient_email VARCHAR(100) NOT NULL COMMENT '接收邮箱',
    send_status ENUM('pending', 'sent', 'failed') NOT NULL COMMENT '发送状态：pending/sent/failed',
    error_message TEXT NULL COMMENT '错误信息',
    sent_at DATETIME NULL COMMENT '发送时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (subscription_id) REFERENCES report_subscriptions(id) ON DELETE CASCADE,
    FOREIGN KEY (report_instance_id) REFERENCES report_instances(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报表发送记录表';

-- ===========================================
-- 7. 系统管理模块表
-- ===========================================

-- 系统配置表
CREATE TABLE system_configs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID，主键',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键，唯一',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('string', 'int', 'float', 'bool', 'json') NOT NULL DEFAULT 'string' COMMENT '配置类型：string/int/float/bool/json',
    category VARCHAR(50) NOT NULL COMMENT '配置分类',
    description TEXT NULL COMMENT '配置描述',
    is_editable BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否可编辑',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE operation_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID，主键',
    user_id INT NULL COMMENT '操作用户ID',
    username VARCHAR(50) NULL COMMENT '用户名',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_module VARCHAR(50) NOT NULL COMMENT '操作模块',
    operation_desc TEXT NOT NULL COMMENT '操作描述',
    request_method VARCHAR(10) NULL COMMENT '请求方法',
    request_url VARCHAR(500) NULL COMMENT '请求URL',
    request_params JSON NULL COMMENT '请求参数',
    response_code INT NULL COMMENT '响应状态码',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    execution_time INT NULL COMMENT '执行时间（毫秒）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 系统监控表
CREATE TABLE system_monitors (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '监控ID，主键',
    monitor_type VARCHAR(50) NOT NULL COMMENT '监控类型',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    metric_value DECIMAL(15,4) NOT NULL COMMENT '指标值',
    threshold_value DECIMAL(15,4) NULL COMMENT '阈值',
    status ENUM('normal', 'warning', 'critical') NOT NULL DEFAULT 'normal' COMMENT '状态：normal/warning/critical',
    server_name VARCHAR(100) NULL COMMENT '服务器名称',
    metadata JSON NULL COMMENT '元数据',
    collected_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '采集时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统监控表';

-- 数据备份记录表
CREATE TABLE backup_records (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '备份ID，主键',
    backup_name VARCHAR(100) NOT NULL COMMENT '备份名称',
    backup_type ENUM('full', 'incremental', 'differential') NOT NULL COMMENT '备份类型：full/incremental/differential',
    backup_path VARCHAR(500) NOT NULL COMMENT '备份路径',
    file_size BIGINT NOT NULL COMMENT '文件大小',
    backup_status ENUM('running', 'completed', 'failed') NOT NULL DEFAULT 'running' COMMENT '状态：running/completed/failed',
    start_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time DATETIME NULL COMMENT '结束时间',
    error_message TEXT NULL COMMENT '错误信息',
    created_by INT NOT NULL COMMENT '创建人ID',
    FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据备份记录表';

-- 系统告警表
CREATE TABLE system_alerts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '告警ID，主键',
    alert_type VARCHAR(50) NOT NULL COMMENT '告警类型',
    alert_level ENUM('info', 'warning', 'error', 'critical') NOT NULL COMMENT '告警级别：info/warning/error/critical',
    alert_title VARCHAR(200) NOT NULL COMMENT '告警标题',
    alert_content TEXT NOT NULL COMMENT '告警内容',
    alert_source VARCHAR(100) NULL COMMENT '告警来源',
    is_read BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已读',
    is_handled BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已处理',
    handled_by INT NULL COMMENT '处理人ID',
    handled_at DATETIME NULL COMMENT '处理时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (handled_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统告警表';

-- ===========================================
-- 8. 创建索引
-- ===========================================

-- 用户管理模块索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);

-- 商品相关索引
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_products_price ON products(price);
CREATE INDEX idx_product_categories_parent_id ON product_categories(parent_id);
CREATE INDEX idx_product_categories_status ON product_categories(status);

-- 订单相关索引
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(order_status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_order_no ON orders(order_no);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);

-- 用户行为分析索引
CREATE INDEX idx_user_behaviors_user_id ON user_behaviors(user_id);
CREATE INDEX idx_user_behaviors_session_id ON user_behaviors(session_id);
CREATE INDEX idx_user_behaviors_behavior_type ON user_behaviors(behavior_type);
CREATE INDEX idx_user_behaviors_created_at ON user_behaviors(created_at);
CREATE INDEX idx_user_behaviors_target ON user_behaviors(target_type, target_id);

-- 页面访问索引
CREATE INDEX idx_page_views_user_id ON page_views(user_id);
CREATE INDEX idx_page_views_session_id ON page_views(session_id);
CREATE INDEX idx_page_views_page_url ON page_views(page_url);
CREATE INDEX idx_page_views_created_at ON page_views(created_at);

-- 搜索记录索引
CREATE INDEX idx_search_logs_user_id ON search_logs(user_id);
CREATE INDEX idx_search_logs_keyword ON search_logs(search_keyword);
CREATE INDEX idx_search_logs_created_at ON search_logs(created_at);

-- 分析结果索引
CREATE INDEX idx_analysis_results_type ON analysis_results(analysis_type);
CREATE INDEX idx_analysis_results_date_range ON analysis_results(date_range_start, date_range_end);
CREATE INDEX idx_analysis_results_status ON analysis_results(status);
CREATE INDEX idx_analysis_results_created_by ON analysis_results(created_by);

-- 用户标签索引
CREATE INDEX idx_user_tags_user_id ON user_tags(user_id);
CREATE INDEX idx_user_tags_type ON user_tags(tag_type);

-- 商品热度索引
CREATE INDEX idx_product_popularity_product_id ON product_popularity(product_id);
CREATE INDEX idx_product_popularity_date ON product_popularity(date);
CREATE INDEX idx_product_popularity_score ON product_popularity(popularity_score);

-- 可视化配置索引
CREATE INDEX idx_dashboard_configs_created_by ON dashboard_configs(created_by);
CREATE INDEX idx_dashboard_configs_is_public ON dashboard_configs(is_public);
CREATE INDEX idx_chart_configs_created_by ON chart_configs(created_by);
CREATE INDEX idx_chart_configs_chart_type ON chart_configs(chart_type);

-- 报表管理索引
CREATE INDEX idx_report_templates_type ON report_templates(template_type);
CREATE INDEX idx_report_templates_status ON report_templates(status);
CREATE INDEX idx_report_instances_template_id ON report_instances(template_id);
CREATE INDEX idx_report_instances_status ON report_instances(generation_status);
CREATE INDEX idx_report_instances_generated_at ON report_instances(generated_at);
CREATE INDEX idx_report_subscriptions_template_id ON report_subscriptions(template_id);
CREATE INDEX idx_report_subscriptions_user_id ON report_subscriptions(user_id);
CREATE INDEX idx_report_subscriptions_is_active ON report_subscriptions(is_active);

-- 系统管理索引
CREATE INDEX idx_system_configs_category ON system_configs(category);
CREATE INDEX idx_operation_logs_user_id ON operation_logs(user_id);
CREATE INDEX idx_operation_logs_created_at ON operation_logs(created_at);
CREATE INDEX idx_operation_logs_operation_type ON operation_logs(operation_type);
CREATE INDEX idx_operation_logs_ip_address ON operation_logs(ip_address);
CREATE INDEX idx_system_monitors_type_name ON system_monitors(monitor_type, metric_name);
CREATE INDEX idx_system_monitors_collected_at ON system_monitors(collected_at);
CREATE INDEX idx_system_monitors_status ON system_monitors(status);
CREATE INDEX idx_backup_records_status ON backup_records(backup_status);
CREATE INDEX idx_backup_records_start_time ON backup_records(start_time);
CREATE INDEX idx_system_alerts_level ON system_alerts(alert_level);
CREATE INDEX idx_system_alerts_created_at ON system_alerts(created_at);
CREATE INDEX idx_system_alerts_is_handled ON system_alerts(is_handled);

-- ===========================================
-- 9. 插入系统基础配置数据
-- ===========================================

INSERT INTO system_configs (config_key, config_value, config_type, category, description, is_editable) VALUES
-- 数据管理配置
('data_retention_days', '365', 'int', 'data_management', '数据保留天数', TRUE),
('log_retention_days', '90', 'int', 'data_management', '日志保留天数', TRUE),
('backup_retention_days', '30', 'int', 'data_management', '备份保留天数', TRUE),
('auto_backup_enabled', 'true', 'bool', 'data_management', '是否启用自动备份', TRUE),
('backup_schedule', '0 2 * * *', 'string', 'data_management', '备份计划（Cron表达式）', TRUE),

-- 邮件配置
('email_smtp_server', 'smtp.example.com', 'string', 'email', '邮件SMTP服务器', TRUE),
('email_smtp_port', '587', 'int', 'email', '邮件SMTP端口', TRUE),
('email_smtp_username', '', 'string', 'email', '邮件SMTP用户名', TRUE),
('email_smtp_password', '', 'string', 'email', '邮件SMTP密码', TRUE),
('email_from_address', '<EMAIL>', 'string', 'email', '发件人邮箱', TRUE),
('email_enabled', 'false', 'bool', 'email', '是否启用邮件功能', TRUE),

-- 系统监控配置
('cpu_usage_threshold', '80.0', 'float', 'monitoring', 'CPU使用率告警阈值（%）', TRUE),
('memory_usage_threshold', '85.0', 'float', 'monitoring', '内存使用率告警阈值（%）', TRUE),
('disk_usage_threshold', '90.0', 'float', 'monitoring', '磁盘使用率告警阈值（%）', TRUE),
('error_rate_threshold', '0.05', 'float', 'monitoring', '错误率告警阈值', TRUE),
('monitor_interval', '60', 'int', 'monitoring', '监控数据采集间隔（秒）', TRUE),

-- 分析配置
('analysis_batch_size', '10000', 'int', 'analysis', '分析批处理大小', TRUE),
('realtime_cache_ttl', '300', 'int', 'analysis', '实时数据缓存TTL（秒）', TRUE),
('funnel_timeout_minutes', '30', 'int', 'analysis', '漏斗分析超时时间（分钟）', TRUE),

-- 安全配置
('session_timeout_hours', '24', 'int', 'security', '会话超时时间（小时）', TRUE),
('max_login_attempts', '5', 'int', 'security', '最大登录尝试次数', TRUE),
('password_min_length', '6', 'int', 'security', '密码最小长度', TRUE),
('api_rate_limit', '1000', 'int', 'security', 'API速率限制（每小时）', TRUE),

-- 应用配置
('app_name', '电商用户行为分析系统', 'string', 'application', '应用名称', TRUE),
('app_version', '1.0.0', 'string', 'application', '应用版本', FALSE),
('max_file_upload_size', '50', 'int', 'application', '最大文件上传大小（MB）', TRUE),
('default_page_size', '20', 'int', 'application', '默认分页大小', TRUE);

-- ===========================================
-- 10. 插入基础商品分类数据
-- ===========================================

INSERT INTO product_categories (name, parent_id, level, sort_order) VALUES
('电子产品', NULL, 1, 1),
('服装鞋帽', NULL, 1, 2),
('家居生活', NULL, 1, 3),
('图书音像', NULL, 1, 4),
('运动户外', NULL, 1, 5),

-- 电子产品子分类
('手机通讯', 1, 2, 1),
('电脑办公', 1, 2, 2),
('数码相机', 1, 2, 3),
('智能穿戴', 1, 2, 4),

-- 服装鞋帽子分类
('男装', 2, 2, 1),
('女装', 2, 2, 2),
('童装', 2, 2, 3),
('鞋靴', 2, 2, 4),

-- 家居生活子分类
('家具', 3, 2, 1),
('家纺', 3, 2, 2),
('厨具', 3, 2, 3),
('家电', 3, 2, 4);

-- ===========================================
-- 11. 创建默认管理员用户
-- ===========================================

INSERT INTO users (username, email, password, real_name, role, status) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewBgIaZt5EXHlRm6', '系统管理员', 'admin', 'active');
-- 注意：这里的密码是 'admin123' 的bcrypt哈希值，实际部署时应该修改

-- ===========================================
-- 12. 创建分区表（适用于大数据量场景）
-- ===========================================

-- 为用户行为表按月分区（示例）
-- ALTER TABLE user_behaviors PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
--     PARTITION p202401 VALUES LESS THAN (202402),
--     PARTITION p202402 VALUES LESS THAN (202403),
--     PARTITION p202403 VALUES LESS THAN (202404),
--     PARTITION p202404 VALUES LESS THAN (202405),
--     PARTITION p202405 VALUES LESS THAN (202406),
--     PARTITION p202406 VALUES LESS THAN (202407),
--     PARTITION p202407 VALUES LESS THAN (202408),
--     PARTITION p202408 VALUES LESS THAN (202409),
--     PARTITION p202409 VALUES LESS THAN (202410),
--     PARTITION p202410 VALUES LESS THAN (202411),
--     PARTITION p202411 VALUES LESS THAN (202412),
--     PARTITION p202412 VALUES LESS THAN (202501),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- ===========================================
-- 脚本执行完成
-- ===========================================

-- 显示所有表的信息
SELECT 
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    TABLE_ROWS as '预估行数',
    ROUND((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024, 2) as '大小(MB)'
FROM 
    information_schema.TABLES 
WHERE 
    TABLE_SCHEMA = 'ecommerce_analytics' 
ORDER BY 
    TABLE_NAME;

-- 显示索引信息统计
SELECT 
    COUNT(*) as '索引总数'
FROM 
    information_schema.STATISTICS 
WHERE 
    TABLE_SCHEMA = 'ecommerce_analytics';

SHOW TABLES;

-- 脚本执行完成提示
SELECT '数据库初始化完成！' as '状态', NOW() as '完成时间';
