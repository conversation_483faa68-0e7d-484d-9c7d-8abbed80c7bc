# 电商用户行为分析系统数据库设计说明

## 1. 概述

本文档详细说明了电商用户行为分析系统的数据库设计，包括表结构、索引、约束关系等，为系统开发和维护提供参考。

### 1.1 数据库基本信息
- **数据库名称**: `ecommerce_analytics`
- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_unicode_ci`
- **存储引擎**: `InnoDB`
- **MySQL版本要求**: 8.0+

### 1.2 设计原则
- 遵循第三范式，避免数据冗余
- 合理使用索引，平衡查询性能和存储空间
- 支持大数据量存储和查询
- 考虑数据分区和分表策略
- 保证数据完整性和一致性

## 2. 数据库表结构详述

### 2.1 用户管理模块（2张表）

#### 2.1.1 users - 用户表
**表说明**: 存储系统用户的基本信息，包括管理员和普通用户。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PK, AUTO_INCREMENT | 用户唯一标识 |
| username | VARCHAR(50) | NOT NULL, UNIQUE | 用户名，登录凭证 |
| email | VARCHAR(100) | NOT NULL, UNIQUE | 邮箱，登录凭证 |
| password | VARCHAR(128) | NOT NULL | 密码（bcrypt加密） |
| real_name | VARCHAR(50) | NULL | 真实姓名 |
| phone | VARCHAR(20) | NULL | 手机号码 |
| avatar | VARCHAR(255) | NULL | 头像图片URL |
| role | ENUM | NOT NULL, DEFAULT 'user' | 用户角色：admin/user |
| status | ENUM | NOT NULL, DEFAULT 'active' | 账户状态：active/inactive/banned |
| last_login | DATETIME | NULL | 最后登录时间 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**:
- `idx_users_email`: 邮箱登录查询
- `idx_users_username`: 用户名登录查询
- `idx_users_status`: 用户状态筛选
- `idx_users_created_at`: 注册时间排序

#### 2.1.2 user_sessions - 用户会话表
**表说明**: 记录用户登录会话信息，用于session管理和安全追踪。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PK, AUTO_INCREMENT | 会话唯一标识 |
| user_id | INT | NOT NULL, FK | 关联用户ID |
| session_key | VARCHAR(255) | NOT NULL | 会话密钥 |
| ip_address | VARCHAR(45) | NULL | 登录IP地址 |
| user_agent | TEXT | NULL | 浏览器信息 |
| expires_at | DATETIME | NOT NULL | 会话过期时间 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |

**外键关系**:
- `user_id` → `users.id` (CASCADE DELETE)

### 2.2 基础数据模块（4张表）

#### 2.2.1 product_categories - 商品分类表
**表说明**: 商品分类的层级结构，支持多级分类。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PK, AUTO_INCREMENT | 分类唯一标识 |
| name | VARCHAR(100) | NOT NULL | 分类名称 |
| parent_id | INT | NULL, FK | 父分类ID |
| level | INT | NOT NULL, DEFAULT 1 | 分类级别 |
| sort_order | INT | NOT NULL, DEFAULT 0 | 排序序号 |
| status | ENUM | NOT NULL, DEFAULT 'active' | 状态：active/inactive |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**外键关系**:
- `parent_id` → `product_categories.id` (SET NULL)

#### 2.2.2 products - 商品表
**表说明**: 商品基本信息，电商系统的核心实体。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PK, AUTO_INCREMENT | 商品唯一标识 |
| name | VARCHAR(200) | NOT NULL | 商品名称 |
| category_id | INT | NOT NULL, FK | 商品分类ID |
| brand | VARCHAR(100) | NULL | 商品品牌 |
| price | DECIMAL(10,2) | NOT NULL | 当前价格 |
| original_price | DECIMAL(10,2) | NULL | 原价（划线价） |
| description | TEXT | NULL | 商品详细描述 |
| image_url | VARCHAR(500) | NULL | 商品主图URL |
| stock | INT | NOT NULL, DEFAULT 0 | 库存数量 |
| status | ENUM | NOT NULL, DEFAULT 'active' | 商品状态：active/inactive/deleted |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**外键关系**:
- `category_id` → `product_categories.id`

#### 2.2.3 orders - 订单表
**表说明**: 用户订单主表，记录订单的基本信息。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 订单唯一标识 |
| order_no | VARCHAR(50) | NOT NULL, UNIQUE | 订单号 |
| user_id | INT | NOT NULL, FK | 下单用户ID |
| total_amount | DECIMAL(10,2) | NOT NULL | 订单总金额 |
| discount_amount | DECIMAL(10,2) | NOT NULL, DEFAULT 0 | 优惠金额 |
| shipping_fee | DECIMAL(10,2) | NOT NULL, DEFAULT 0 | 运费 |
| payment_method | ENUM | NULL | 支付方式：alipay/wechat/credit_card |
| order_status | ENUM | NOT NULL, DEFAULT 'pending' | 订单状态：pending/paid/shipped/completed/cancelled |
| shipping_address | TEXT | NULL | 收货地址 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**外键关系**:
- `user_id` → `users.id`

#### 2.2.4 order_items - 订单详情表
**表说明**: 订单商品明细，记录每个订单包含的商品信息。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 明细唯一标识 |
| order_id | BIGINT | NOT NULL, FK | 所属订单ID |
| product_id | INT | NOT NULL, FK | 商品ID |
| product_name | VARCHAR(200) | NOT NULL | 商品名称（冗余存储） |
| quantity | INT | NOT NULL | 购买数量 |
| unit_price | DECIMAL(10,2) | NOT NULL | 商品单价 |
| total_price | DECIMAL(10,2) | NOT NULL | 小计金额 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |

**外键关系**:
- `order_id` → `orders.id` (CASCADE DELETE)
- `product_id` → `products.id`

### 2.3 数据采集模块（3张表）

#### 2.3.1 user_behaviors - 用户行为表
**表说明**: 记录用户在网站上的各种行为，是分析的基础数据。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 行为记录唯一标识 |
| user_id | INT | NULL, FK | 用户ID（匿名用户为NULL） |
| session_id | VARCHAR(100) | NOT NULL | 会话标识 |
| behavior_type | ENUM | NOT NULL | 行为类型：view/click/search/add_cart/remove_cart/purchase |
| target_type | ENUM | NULL | 目标类型：product/category/page |
| target_id | INT | NULL | 目标对象ID |
| page_url | VARCHAR(500) | NULL | 当前页面URL |
| referrer_url | VARCHAR(500) | NULL | 来源页面URL |
| ip_address | VARCHAR(45) | NULL | 用户IP地址 |
| user_agent | TEXT | NULL | 浏览器信息 |
| device_type | ENUM | NULL | 设备类型：desktop/mobile/tablet |
| browser | VARCHAR(50) | NULL | 浏览器名称 |
| os | VARCHAR(50) | NULL | 操作系统 |
| location | VARCHAR(100) | NULL | 地理位置 |
| duration | INT | NULL | 停留时长（秒） |
| extra_data | JSON | NULL | 扩展数据 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 行为发生时间 |

**外键关系**:
- `user_id` → `users.id` (SET NULL)

**重要索引**:
- `idx_user_behaviors_user_id`: 按用户查询行为
- `idx_user_behaviors_session_id`: 按会话查询行为
- `idx_user_behaviors_behavior_type`: 按行为类型分析
- `idx_user_behaviors_created_at`: 按时间范围查询
- `idx_user_behaviors_target`: 按目标对象查询

#### 2.3.2 page_views - 页面访问表
**表说明**: 专门记录页面访问（PV）数据，用于流量分析。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 访问记录唯一标识 |
| user_id | INT | NULL, FK | 用户ID |
| session_id | VARCHAR(100) | NOT NULL | 会话标识 |
| page_url | VARCHAR(500) | NOT NULL | 访问页面URL |
| page_title | VARCHAR(200) | NULL | 页面标题 |
| referrer_url | VARCHAR(500) | NULL | 来源页面URL |
| ip_address | VARCHAR(45) | NULL | 访问IP地址 |
| user_agent | TEXT | NULL | 浏览器信息 |
| load_time | INT | NULL | 页面加载时间（毫秒） |
| stay_time | INT | NULL | 页面停留时间（秒） |
| scroll_depth | DECIMAL(5,2) | NULL | 页面滚动深度（百分比） |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 访问时间 |

**外键关系**:
- `user_id` → `users.id` (SET NULL)

#### 2.3.3 search_logs - 搜索记录表
**表说明**: 记录用户搜索行为，用于搜索优化和商品推荐。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 搜索记录唯一标识 |
| user_id | INT | NULL, FK | 用户ID |
| session_id | VARCHAR(100) | NOT NULL | 会话标识 |
| search_keyword | VARCHAR(200) | NOT NULL | 搜索关键词 |
| search_type | ENUM | NOT NULL, DEFAULT 'product' | 搜索类型：product/category/article |
| result_count | INT | NULL | 搜索结果数量 |
| click_position | INT | NULL | 点击结果位置 |
| click_product_id | INT | NULL, FK | 点击的商品ID |
| ip_address | VARCHAR(45) | NULL | 搜索IP地址 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 搜索时间 |

**外键关系**:
- `user_id` → `users.id` (SET NULL)
- `click_product_id` → `products.id` (SET NULL)

### 2.4 数据分析模块（4张表）

#### 2.4.1 analysis_results - 分析结果表
**表说明**: 存储各种数据分析的结果，支持结果缓存和历史查询。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 分析结果唯一标识 |
| analysis_type | VARCHAR(50) | NOT NULL | 分析类型标识 |
| analysis_name | VARCHAR(100) | NOT NULL | 分析名称 |
| date_range_start | DATE | NOT NULL | 分析数据开始日期 |
| date_range_end | DATE | NOT NULL | 分析数据结束日期 |
| result_data | JSON | NOT NULL | 分析结果数据（JSON格式） |
| status | ENUM | NOT NULL, DEFAULT 'pending' | 分析状态：pending/completed/failed |
| created_by | INT | NOT NULL, FK | 分析发起人 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**外键关系**:
- `created_by` → `users.id`

#### 2.4.2 user_tags - 用户标签表
**表说明**: 用户画像标签，基于行为分析生成的用户特征标签。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PK, AUTO_INCREMENT | 标签记录唯一标识 |
| user_id | INT | NOT NULL, FK | 用户ID |
| tag_type | VARCHAR(50) | NOT NULL | 标签类型（如：消费等级、兴趣偏好） |
| tag_value | VARCHAR(100) | NOT NULL | 标签值 |
| score | DECIMAL(5,2) | NULL | 标签置信度分数 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**外键关系**:
- `user_id` → `users.id` (CASCADE DELETE)

**唯一约束**:
- `uk_user_tag`: (user_id, tag_type) - 用户同类型标签唯一

#### 2.4.3 product_popularity - 商品热度表
**表说明**: 商品热度统计，按日汇总商品的各项指标。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PK, AUTO_INCREMENT | 记录唯一标识 |
| product_id | INT | NOT NULL, FK | 商品ID |
| date | DATE | NOT NULL | 统计日期 |
| view_count | INT | NOT NULL, DEFAULT 0 | 浏览次数 |
| click_count | INT | NOT NULL, DEFAULT 0 | 点击次数 |
| cart_count | INT | NOT NULL, DEFAULT 0 | 加购次数 |
| order_count | INT | NOT NULL, DEFAULT 0 | 下单次数 |
| conversion_rate | DECIMAL(5,4) | NOT NULL, DEFAULT 0 | 转化率 |
| popularity_score | DECIMAL(8,2) | NOT NULL, DEFAULT 0 | 综合热度分数 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |

**外键关系**:
- `product_id` → `products.id` (CASCADE DELETE)

**唯一约束**:
- `uk_product_date`: (product_id, date) - 商品每日统计唯一

#### 2.4.4 funnel_analysis - 漏斗分析表
**表说明**: 存储漏斗分析结果，用于转化路径分析。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PK, AUTO_INCREMENT | 分析记录唯一标识 |
| funnel_name | VARCHAR(100) | NOT NULL | 漏斗名称 |
| step_name | VARCHAR(100) | NOT NULL | 步骤名称 |
| step_order | INT | NOT NULL | 步骤顺序 |
| date | DATE | NOT NULL | 统计日期 |
| user_count | INT | NOT NULL, DEFAULT 0 | 该步骤用户数 |
| conversion_rate | DECIMAL(5,4) | NOT NULL, DEFAULT 0 | 到下一步的转化率 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |

**唯一约束**:
- `uk_funnel_step_date`: (funnel_name, step_order, date) - 漏斗步骤日期唯一

### 2.5 数据可视化模块（3张表）

#### 2.5.1 dashboard_configs - 仪表盘配置表
**表说明**: 用户自定义仪表盘的配置信息。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PK, AUTO_INCREMENT | 仪表盘唯一标识 |
| name | VARCHAR(100) | NOT NULL | 仪表盘名称 |
| description | TEXT | NULL | 仪表盘描述 |
| layout_config | JSON | NOT NULL | 布局配置（网格布局） |
| widgets | JSON | NOT NULL | 组件配置列表 |
| is_public | BOOLEAN | NOT NULL, DEFAULT FALSE | 是否公开访问 |
| created_by | INT | NOT NULL, FK | 创建者ID |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**外键关系**:
- `created_by` → `users.id`

#### 2.5.2 chart_configs - 图表配置表
**表说明**: 图表配置信息，支持自定义图表创建。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PK, AUTO_INCREMENT | 图表唯一标识 |
| chart_name | VARCHAR(100) | NOT NULL | 图表名称 |
| chart_type | ENUM | NOT NULL | 图表类型：line/bar/pie/scatter/heatmap |
| data_source | VARCHAR(100) | NOT NULL | 数据源标识 |
| query_config | JSON | NOT NULL | 查询配置 |
| style_config | JSON | NOT NULL | 样式配置 |
| refresh_interval | INT | NOT NULL, DEFAULT 300 | 刷新间隔（秒） |
| created_by | INT | NOT NULL, FK | 创建者ID |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**外键关系**:
- `created_by` → `users.id`

#### 2.5.3 realtime_data_cache - 实时数据缓存表
**表说明**: 实时指标数据缓存，用于大屏展示。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 缓存记录唯一标识 |
| metric_key | VARCHAR(100) | NOT NULL | 指标键名 |
| metric_value | DECIMAL(15,4) | NOT NULL | 指标数值 |
| data_type | ENUM | NOT NULL | 数据类型：counter/gauge/histogram |
| timestamp | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 数据时间戳 |
| metadata | JSON | NULL | 元数据信息 |
| expires_at | DATETIME | NOT NULL | 数据过期时间 |

**重要索引**:
- `idx_metric_key`: 按指标键查询
- `idx_timestamp`: 按时间排序
- `idx_expires_at`: 过期数据清理

### 2.6 报表管理模块（4张表）

#### 2.6.1 report_templates - 报表模板表
**表说明**: 报表模板定义，支持定时生成报表。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PK, AUTO_INCREMENT | 模板唯一标识 |
| template_name | VARCHAR(100) | NOT NULL | 模板名称 |
| template_type | ENUM | NOT NULL | 模板类型：daily/weekly/monthly/custom |
| description | TEXT | NULL | 模板描述 |
| template_config | JSON | NOT NULL | 模板配置（布局、内容） |
| data_sources | JSON | NOT NULL | 数据源配置 |
| layout_config | JSON | NOT NULL | 页面布局配置 |
| status | ENUM | NOT NULL, DEFAULT 'active' | 模板状态：active/inactive |
| created_by | INT | NOT NULL, FK | 创建者ID |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**外键关系**:
- `created_by` → `users.id`

#### 2.6.2 report_instances - 报表实例表
**表说明**: 生成的报表实例，记录每次报表生成的结果。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 报表实例唯一标识 |
| template_id | INT | NOT NULL, FK | 使用的模板ID |
| report_name | VARCHAR(100) | NOT NULL | 报表名称 |
| report_period | VARCHAR(50) | NOT NULL | 报表周期标识 |
| start_date | DATE | NOT NULL | 数据开始日期 |
| end_date | DATE | NOT NULL | 数据结束日期 |
| report_data | JSON | NULL | 报表数据内容 |
| file_path | VARCHAR(500) | NULL | 生成文件路径 |
| file_size | BIGINT | NULL | 文件大小（字节） |
| generation_status | ENUM | NOT NULL, DEFAULT 'pending' | 生成状态：pending/generating/completed/failed |
| error_message | TEXT | NULL | 错误信息 |
| generated_by | INT | NOT NULL, FK | 生成人ID |
| generated_at | DATETIME | NULL | 生成完成时间 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |

**外键关系**:
- `template_id` → `report_templates.id`
- `generated_by` → `users.id`

#### 2.6.3 report_subscriptions - 报表订阅表
**表说明**: 用户报表订阅配置，支持自动发送报表。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PK, AUTO_INCREMENT | 订阅唯一标识 |
| template_id | INT | NOT NULL, FK | 订阅的模板ID |
| user_id | INT | NOT NULL, FK | 订阅用户ID |
| email | VARCHAR(100) | NOT NULL | 接收邮箱 |
| schedule_type | ENUM | NOT NULL | 调度类型：daily/weekly/monthly |
| schedule_config | JSON | NOT NULL | 调度配置（时间设置） |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE | 是否启用订阅 |
| last_sent_at | DATETIME | NULL | 最后发送时间 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**外键关系**:
- `template_id` → `report_templates.id` (CASCADE DELETE)
- `user_id` → `users.id` (CASCADE DELETE)

#### 2.6.4 report_send_logs - 报表发送记录表
**表说明**: 报表邮件发送记录，用于追踪发送状态。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 发送记录唯一标识 |
| subscription_id | INT | NOT NULL, FK | 订阅ID |
| report_instance_id | BIGINT | NOT NULL, FK | 报表实例ID |
| recipient_email | VARCHAR(100) | NOT NULL | 接收邮箱 |
| send_status | ENUM | NOT NULL | 发送状态：pending/sent/failed |
| error_message | TEXT | NULL | 发送失败错误信息 |
| sent_at | DATETIME | NULL | 实际发送时间 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |

**外键关系**:
- `subscription_id` → `report_subscriptions.id` (CASCADE DELETE)
- `report_instance_id` → `report_instances.id` (CASCADE DELETE)

### 2.7 系统管理模块（5张表）

#### 2.7.1 system_configs - 系统配置表
**表说明**: 系统参数配置，支持动态配置修改。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PK, AUTO_INCREMENT | 配置唯一标识 |
| config_key | VARCHAR(100) | NOT NULL, UNIQUE | 配置键名 |
| config_value | TEXT | NOT NULL | 配置值 |
| config_type | ENUM | NOT NULL, DEFAULT 'string' | 配置数据类型：string/int/float/bool/json |
| category | VARCHAR(50) | NOT NULL | 配置分类 |
| description | TEXT | NULL | 配置说明 |
| is_editable | BOOLEAN | NOT NULL, DEFAULT TRUE | 是否允许编辑 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

#### 2.7.2 operation_logs - 操作日志表
**表说明**: 系统操作审计日志，记录所有重要操作。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 日志唯一标识 |
| user_id | INT | NULL, FK | 操作用户ID |
| username | VARCHAR(50) | NULL | 操作用户名 |
| operation_type | VARCHAR(50) | NOT NULL | 操作类型（CRUD） |
| operation_module | VARCHAR(50) | NOT NULL | 操作模块 |
| operation_desc | TEXT | NOT NULL | 操作描述 |
| request_method | VARCHAR(10) | NULL | HTTP请求方法 |
| request_url | VARCHAR(500) | NULL | 请求URL |
| request_params | JSON | NULL | 请求参数 |
| response_code | INT | NULL | 响应状态码 |
| ip_address | VARCHAR(45) | NULL | 操作IP地址 |
| user_agent | TEXT | NULL | 浏览器信息 |
| execution_time | INT | NULL | 执行时间（毫秒） |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 操作时间 |

**外键关系**:
- `user_id` → `users.id` (SET NULL)

**重要索引**:
- `idx_operation_logs_user_id`: 按用户查询操作
- `idx_operation_logs_created_at`: 按时间查询
- `idx_operation_logs_operation_type`: 按操作类型查询
- `idx_operation_logs_ip_address`: 按IP地址查询

#### 2.7.3 system_monitors - 系统监控表
**表说明**: 系统性能监控数据，用于系统健康状态监控。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 监控记录唯一标识 |
| monitor_type | VARCHAR(50) | NOT NULL | 监控类型（CPU、内存等） |
| metric_name | VARCHAR(100) | NOT NULL | 监控指标名称 |
| metric_value | DECIMAL(15,4) | NOT NULL | 监控指标值 |
| threshold_value | DECIMAL(15,4) | NULL | 告警阈值 |
| status | ENUM | NOT NULL, DEFAULT 'normal' | 状态：normal/warning/critical |
| server_name | VARCHAR(100) | NULL | 服务器名称 |
| metadata | JSON | NULL | 扩展元数据 |
| collected_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 采集时间 |

#### 2.7.4 backup_records - 数据备份记录表
**表说明**: 数据库备份操作记录。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PK, AUTO_INCREMENT | 备份记录唯一标识 |
| backup_name | VARCHAR(100) | NOT NULL | 备份名称 |
| backup_type | ENUM | NOT NULL | 备份类型：full/incremental/differential |
| backup_path | VARCHAR(500) | NOT NULL | 备份文件路径 |
| file_size | BIGINT | NOT NULL | 备份文件大小 |
| backup_status | ENUM | NOT NULL, DEFAULT 'running' | 备份状态：running/completed/failed |
| start_time | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 备份开始时间 |
| end_time | DATETIME | NULL | 备份结束时间 |
| error_message | TEXT | NULL | 错误信息 |
| created_by | INT | NOT NULL, FK | 备份发起人 |

**外键关系**:
- `created_by` → `users.id`

#### 2.7.5 system_alerts - 系统告警表
**表说明**: 系统告警信息，支持多级别告警。

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 告警唯一标识 |
| alert_type | VARCHAR(50) | NOT NULL | 告警类型 |
| alert_level | ENUM | NOT NULL | 告警级别：info/warning/error/critical |
| alert_title | VARCHAR(200) | NOT NULL | 告警标题 |
| alert_content | TEXT | NOT NULL | 告警详细内容 |
| alert_source | VARCHAR(100) | NULL | 告警来源 |
| is_read | BOOLEAN | NOT NULL, DEFAULT FALSE | 是否已读 |
| is_handled | BOOLEAN | NOT NULL, DEFAULT FALSE | 是否已处理 |
| handled_by | INT | NULL, FK | 处理人ID |
| handled_at | DATETIME | NULL | 处理时间 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 告警时间 |

**外键关系**:
- `handled_by` → `users.id` (SET NULL)

## 3. 索引设计策略

### 3.1 索引设计原则
1. **主键索引**: 所有表都有自增主键，保证唯一性和性能
2. **外键索引**: 所有外键字段都创建索引，优化关联查询
3. **查询索引**: 根据常见查询模式创建复合索引
4. **时间索引**: 时间字段创建索引，支持时间范围查询
5. **状态索引**: 状态字段创建索引，优化筛选查询

### 3.2 重要复合索引

#### 用户行为分析相关
```sql
-- 用户行为时间范围查询
CREATE INDEX idx_user_behaviors_user_time ON user_behaviors(user_id, created_at);

-- 行为类型统计
CREATE INDEX idx_user_behaviors_type_time ON user_behaviors(behavior_type, created_at);

-- 目标对象分析
CREATE INDEX idx_user_behaviors_target ON user_behaviors(target_type, target_id);
```

#### 商品分析相关
```sql
-- 商品热度排序
CREATE INDEX idx_product_popularity_score_date ON product_popularity(popularity_score DESC, date);

-- 分类商品查询
CREATE INDEX idx_products_category_status ON products(category_id, status);
```

#### 订单分析相关
```sql
-- 用户订单历史
CREATE INDEX idx_orders_user_time ON orders(user_id, created_at);

-- 订单状态时间
CREATE INDEX idx_orders_status_time ON orders(order_status, created_at);
```

### 3.3 分区表策略

对于大数据量表，建议使用分区策略：

#### 按时间分区
```sql
-- 用户行为表按月分区（示例配置已注释在脚本中）
ALTER TABLE user_behaviors PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at));

-- 页面访问表按月分区
ALTER TABLE page_views PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at));

-- 操作日志表按月分区
ALTER TABLE operation_logs PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at));
```

## 4. 数据完整性约束

### 4.1 外键约束
- 所有关联关系都设置了外键约束
- 根据业务需求设置级联删除或设置NULL
- 重要关联数据使用级联删除确保数据一致性

### 4.2 唯一约束
- 用户表：username、email唯一
- 订单表：order_no唯一
- 用户标签：(user_id, tag_type)唯一
- 商品热度：(product_id, date)唯一

### 4.3 检查约束
```sql
-- 可以添加的检查约束示例
ALTER TABLE products ADD CONSTRAINT chk_price_positive CHECK (price > 0);
ALTER TABLE orders ADD CONSTRAINT chk_amount_positive CHECK (total_amount >= 0);
ALTER TABLE product_popularity ADD CONSTRAINT chk_conversion_rate CHECK (conversion_rate >= 0 AND conversion_rate <= 1);
```

## 5. 初始化数据

### 5.1 系统配置
脚本包含了系统运行必需的基础配置，分为以下几类：
- **数据管理配置**: 数据保留策略、备份设置
- **邮件配置**: SMTP服务器配置
- **监控配置**: 性能告警阈值
- **安全配置**: 会话超时、登录限制
- **应用配置**: 基本应用设置

### 5.2 基础数据
- **商品分类**: 预设二级商品分类结构
- **管理员用户**: 默认admin用户（密码：admin123）

## 6. 性能优化建议

### 6.1 查询优化
1. **分页查询**: 大数据量查询使用LIMIT分页
2. **索引覆盖**: 尽量使用覆盖索引避免回表
3. **查询重写**: 复杂查询考虑拆分或使用中间表
4. **统计信息**: 定期更新表统计信息

### 6.2 存储优化
1. **数据归档**: 定期归档历史数据
2. **表分区**: 大表使用分区策略
3. **压缩存储**: 历史数据考虑压缩存储
4. **读写分离**: 分析查询使用只读副本

### 6.3 维护策略
1. **定期备份**: 建立定期备份策略
2. **索引维护**: 定期重建或优化索引
3. **数据清理**: 定期清理过期数据
4. **性能监控**: 监控慢查询和资源使用

## 7. 扩展性设计

### 7.1 水平扩展
- 核心业务表支持分库分表
- 用户数据按用户ID哈希分片
- 行为数据按时间分片

### 7.2 垂直扩展
- 读写分离架构
- 冷热数据分离
- 缓存层设计

### 7.3 数据迁移
- 预留字段扩展
- 版本化模式变更
- 在线DDL操作

## 8. 安全性考虑

### 8.1 数据安全
- 敏感数据加密存储
- 密码使用bcrypt加密
- 操作日志完整记录

### 8.2 访问控制
- 数据库用户权限分离
- 应用层权限控制
- API访问限制

### 8.3 备份恢复
- 多重备份策略
- 定期恢复测试
- 灾难恢复预案

## 9. 使用说明

### 9.1 执行顺序
1. 创建数据库和基础表结构
2. 创建索引
3. 插入初始化数据
4. 验证表结构和数据

### 9.2 环境要求
- MySQL 8.0 或更高版本
- 足够的存储空间（建议预留数据量的3倍空间）
- 合理的内存配置（建议8GB以上）

### 9.3 监控验证
执行脚本后可以通过以下SQL验证：
```sql
-- 查看所有表
SHOW TABLES;

-- 查看表结构
DESCRIBE table_name;

-- 查看索引
SHOW INDEX FROM table_name;

-- 查看初始数据
SELECT COUNT(*) FROM system_configs;
SELECT COUNT(*) FROM product_categories;
```

这个数据库设计为电商用户行为分析系统提供了完整的数据存储方案，支持大数据量处理和复杂的分析查询需求。
